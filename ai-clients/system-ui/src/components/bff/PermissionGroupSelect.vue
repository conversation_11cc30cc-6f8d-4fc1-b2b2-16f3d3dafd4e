<template>
  <el-select v-model="selectedValue" placeholder="请选择权限组" clearable @change="handleChange">
    <el-option
      v-for="item in permissionGroupList"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  permissionGroupList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 使用计算属性处理 v-model
const selectedValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const handleChange = (value) => {
  emit('change', value)
}
</script> 