import request from '@/utils/request'

// 查询模型输出格式列表
export function listModelOutputFormat(query) {
  return request({
    url: '/system/model/outputFormat/list',
    method: 'get',
    params: query
  })
}

// 查询模型输出格式详细
export function getModelOutputFormat(id) {
  return request({
    url: '/system/model/outputFormat/' + id,
    method: 'get'
  })
}

// 新增模型输出格式
export function addModelOutputFormat(data) {
  return request({
    url: '/system/model/outputFormat',
    method: 'post',
    data: data
  })
}

// 修改模型输出格式
export function updateModelOutputFormat(data) {
  return request({
    url: '/system/model/outputFormat',
    method: 'put',
    data: data
  })
}

// 删除模型输出格式
export function delModelOutputFormat(id) {
  return request({
    url: '/system/model/outputFormat/' + id,
    method: 'delete'
  })
}
