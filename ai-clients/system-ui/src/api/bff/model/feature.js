import request from '@/utils/request'

// 查询模型特性列表
export function listModelFeature(query) {
  return request({
    url: '/system/model/feature/list',
    method: 'get',
    params: query
  })
}

// 查询模型特性详细
export function getModelFeature(id) {
  return request({
    url: '/system/model/feature/' + id,
    method: 'get'
  })
}

// 新增模型特性
export function addModelFeature(data) {
  return request({
    url: '/system/model/feature',
    method: 'post',
    data: data
  })
}

// 修改模型特性
export function updateModelFeature(data) {
  return request({
    url: '/system/model/feature',
    method: 'put',
    data: data
  })
}

// 删除模型特性
export function delModelFeature(id) {
  return request({
    url: '/system/model/feature/' + id,
    method: 'delete'
  })
}
