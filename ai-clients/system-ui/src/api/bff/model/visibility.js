import request from '@/utils/request'

// 查询模型可见性控制列表
export function listModelVisibility(query) {
  return request({
    url: '/system/model/visibility/list',
    method: 'get',
    params: query
  })
}

// 查询模型可见性控制详细
export function getModelVisibility(id) {
  return request({
    url: '/system/model/visibility/' + id,
    method: 'get'
  })
}

// 新增模型可见性控制
export function addModelVisibility(data) {
  return request({
    url: '/system/model/visibility',
    method: 'post',
    data: data
  })
}

// 修改模型可见性控制
export function updateModelVisibility(data) {
  return request({
    url: '/system/model/visibility',
    method: 'put',
    data: data
  })
}

// 删除模型可见性控制
export function delModelVisibility(id) {
  return request({
    url: '/system/model/visibility/' + id,
    method: 'delete'
  })
}
