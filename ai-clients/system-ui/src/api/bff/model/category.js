import request from '@/utils/request'

// 查询模型分类列表
export function listModelCategory(query) {
  return request({
    url: '/system/model/category/list',
    method: 'get',
    params: query
  })
}

// 查询模型分类详细
export function getModelCategory(id) {
  return request({
    url: '/system/model/category/' + id,
    method: 'get'
  })
}

// 新增模型分类
export function addModelCategory(data) {
  return request({
    url: '/system/model/category',
    method: 'post',
    data: data
  })
}

// 修改模型分类
export function updateModelCategory(data) {
  return request({
    url: '/system/model/category',
    method: 'put',
    data: data
  })
}

// 删除模型分类
export function delModelCategory(id) {
  return request({
    url: '/system/model/category/' + id,
    method: 'delete'
  })
}
