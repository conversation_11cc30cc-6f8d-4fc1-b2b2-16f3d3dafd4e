import request from '@/utils/request'

// 查询模型调用请求列表
export function listLogModelRequest(query) {
  return request({
    url: '/system/log/model/request/list',
    method: 'get',
    params: query
  })
}

// 查询模型调用请求详细
export function getLogModelRequest(id) {
  return request({
    url: '/system/log/model/request/' + id,
    method: 'get'
  })
}

// 新增模型调用请求
export function addLogModelRequest(data) {
  return request({
    url: '/system/log/model/request',
    method: 'post',
    data: data
  })
}

// 修改模型调用请求
export function updateLogModelRequest(data) {
  return request({
    url: '/system/log/model/request',
    method: 'put',
    data: data
  })
}

// 删除模型调用请求
export function delLogModelRequest(id) {
  return request({
    url: '/system/log/model/request/' + id,
    method: 'delete'
  })
}
