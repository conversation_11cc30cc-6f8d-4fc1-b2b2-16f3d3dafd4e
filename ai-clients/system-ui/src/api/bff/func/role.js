import request from '@/utils/request'

// 查询角色定义列表
export function listFuncRole(query) {
  return request({
    url: '/system/func/role/list',
    method: 'get',
    params: query
  })
}

// 查询角色定义详细
export function getFuncRole(id) {
  return request({
    url: '/system/func/role/' + id,
    method: 'get'
  })
}

// 新增角色定义
export function addFuncRole(data) {
  return request({
    url: '/system/func/role',
    method: 'post',
    data: data
  })
}

// 修改角色定义
export function updateFuncRole(data) {
  return request({
    url: '/system/func/role',
    method: 'put',
    data: data
  })
}

// 删除角色定义
export function delFuncRole(id) {
  return request({
    url: '/system/func/role/' + id,
    method: 'delete'
  })
}
