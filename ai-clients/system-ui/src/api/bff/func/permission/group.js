import request from '@/utils/request'

// 查询权限分组列表
export function listFuncPermissionGroup(query) {
  return request({
    url: '/system/func/permission/group/list',
    method: 'get',
    params: query
  })
}

// 查询权限分组详细
export function getFuncPermissionGroup(id) {
  return request({
    url: '/system/func/permission/group/' + id,
    method: 'get'
  })
}

// 新增权限分组
export function addFuncPermissionGroup(data) {
  return request({
    url: '/system/func/permission/group',
    method: 'post',
    data: data
  })
}

// 修改权限分组
export function updateFuncPermissionGroup(data) {
  return request({
    url: '/system/func/permission/group',
    method: 'put',
    data: data
  })
}

// 删除权限分组
export function delFuncPermissionGroup(id) {
  return request({
    url: '/system/func/permission/group/' + id,
    method: 'delete'
  })
}
