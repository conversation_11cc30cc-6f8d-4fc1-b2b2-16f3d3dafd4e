import request from '@/utils/request'

// 查询前端功能菜单列表
export function listFuncMenu(query) {
  return request({
    url: '/system/func/menu/list',
    method: 'get',
    params: query
  })
}

// 查询前端功能菜单详细
export function getFuncMenu(id) {
  return request({
    url: '/system/func/menu/' + id,
    method: 'get'
  })
}

// 新增前端功能菜单
export function addFuncMenu(data) {
  return request({
    url: '/system/func/menu',
    method: 'post',
    data: data
  })
}

// 修改前端功能菜单
export function updateFuncMenu(data) {
  return request({
    url: '/system/func/menu',
    method: 'put',
    data: data
  })
}

// 删除前端功能菜单
export function delFuncMenu(id) {
  return request({
    url: '/system/func/menu/' + id,
    method: 'delete'
  })
}
