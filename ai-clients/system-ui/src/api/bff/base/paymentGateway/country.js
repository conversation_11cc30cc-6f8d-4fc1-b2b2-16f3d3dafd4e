import request from '@/utils/request'

// 查询支付网关与国家/地区的映射列表
export function listPaymentGatewayCountry(query) {
  return request({
    url: '/system/base/paymentGateway/country/list',
    method: 'get',
    params: query
  })
}

// 查询支付网关与国家/地区的映射详细
export function getPaymentGatewayCountry(id) {
  return request({
    url: '/system/base/paymentGateway/country/' + id,
    method: 'get'
  })
}

// 新增支付网关与国家/地区的映射
export function addPaymentGatewayCountry(data) {
  return request({
    url: '/system/base/paymentGateway/country',
    method: 'post',
    data: data
  })
}

// 修改支付网关与国家/地区的映射
export function updatePaymentGatewayCountry(data) {
  return request({
    url: '/system/base/paymentGateway/country',
    method: 'put',
    data: data
  })
}

// 删除支付网关与国家/地区的映射
export function delPaymentGatewayCountry(id) {
  return request({
    url: '/system/base/paymentGateway/country/' + id,
    method: 'delete'
  })
}
