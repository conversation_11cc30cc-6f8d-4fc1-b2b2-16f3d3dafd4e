import request from '@/utils/request'

// 查询货币列表
export function listBaseCurrency(query) {
  return request({
    url: '/system/base/currency/list',
    method: 'get',
    params: query
  })
}

// 查询货币详细
export function getBaseCurrency(id) {
  return request({
    url: '/system/base/currency/' + id,
    method: 'get'
  })
}

// 新增货币
export function addBaseCurrency(data) {
  return request({
    url: '/system/base/currency',
    method: 'post',
    data: data
  })
}

// 修改货币
export function updateBaseCurrency(data) {
  return request({
    url: '/system/base/currency',
    method: 'put',
    data: data
  })
}

// 删除货币
export function delBaseCurrency(id) {
  return request({
    url: '/system/base/currency/' + id,
    method: 'delete'
  })
}
