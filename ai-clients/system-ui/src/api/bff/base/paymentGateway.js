import request from '@/utils/request'

// 查询支付网关配置列表
export function listPaymentGateway(query) {
  return request({
    url: '/system/base/paymentGateway/list',
    method: 'get',
    params: query
  })
}

// 查询支付网关配置详细
export function getPaymentGateway(id) {
  return request({
    url: '/system/base/paymentGateway/' + id,
    method: 'get'
  })
}

// 新增支付网关配置
export function addPaymentGateway(data) {
  return request({
    url: '/system/base/paymentGateway',
    method: 'post',
    data: data
  })
}

// 修改支付网关配置
export function updatePaymentGateway(data) {
  return request({
    url: '/system/base/paymentGateway',
    method: 'put',
    data: data
  })
}

// 删除支付网关配置
export function delPaymentGateway(id) {
  return request({
    url: '/system/base/paymentGateway/' + id,
    method: 'delete'
  })
}
