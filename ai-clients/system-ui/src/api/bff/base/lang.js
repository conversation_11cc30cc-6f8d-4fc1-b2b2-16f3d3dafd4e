import request from '@/utils/request'

// 查询语言列表
export function listBaseLang(query) {
  return request({
    url: '/system/base/lang/list',
    method: 'get',
    params: query
  })
}

// 查询语言详细
export function getBaseLang(id) {
  return request({
    url: '/system/base/lang/' + id,
    method: 'get'
  })
}

// 新增语言
export function addBaseLang(data) {
  return request({
    url: '/system/base/lang',
    method: 'post',
    data: data
  })
}

// 修改语言
export function updateBaseLang(data) {
  return request({
    url: '/system/base/lang',
    method: 'put',
    data: data
  })
}

// 删除语言
export function delBaseLang(id) {
  return request({
    url: '/system/base/lang/' + id,
    method: 'delete'
  })
}
