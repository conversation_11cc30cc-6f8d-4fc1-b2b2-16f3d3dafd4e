import request from '@/utils/request'

// 查询静态文档列表
export function listBaseDocument(query) {
  return request({
    url: '/system/base/document/list',
    method: 'get',
    params: query
  })
}

// 查询静态文档详细
export function getBaseDocument(id) {
  return request({
    url: '/system/base/document/' + id,
    method: 'get'
  })
}

// 新增静态文档
export function addBaseDocument(data) {
  return request({
    url: '/system/base/document',
    method: 'post',
    data: data
  })
}

// 修改静态文档
export function updateBaseDocument(data) {
  return request({
    url: '/system/base/document',
    method: 'put',
    data: data
  })
}

// 删除静态文档
export function delBaseDocument(id) {
  return request({
    url: '/system/base/document/' + id,
    method: 'delete'
  })
}
