import request from '@/utils/request'

// 查询计费方案列表
export function listBillingPlan(query) {
  return request({
    url: '/system/billing/plan/list',
    method: 'get',
    params: query
  })
}

// 查询计费方案详细
export function getBillingPlan(id) {
  return request({
    url: '/system/billing/plan/' + id,
    method: 'get'
  })
}

// 新增计费方案
export function addBillingPlan(data) {
  return request({
    url: '/system/billing/plan',
    method: 'post',
    data: data
  })
}

// 修改计费方案
export function updateBillingPlan(data) {
  return request({
    url: '/system/billing/plan',
    method: 'put',
    data: data
  })
}

// 删除计费方案
export function delBillingPlan(id) {
  return request({
    url: '/system/billing/plan/' + id,
    method: 'delete'
  })
}
