import request from '@/utils/request'

// 查询会员消费/充值流水列表
export function listBillingTransaction(query) {
  return request({
    url: '/system/billing/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询会员消费/充值流水详细
export function getBillingTransaction(id) {
  return request({
    url: '/system/billing/transaction/' + id,
    method: 'get'
  })
}

// 新增会员消费/充值流水
export function addBillingTransaction(data) {
  return request({
    url: '/system/billing/transaction',
    method: 'post',
    data: data
  })
}

// 修改会员消费/充值流水
export function updateBillingTransaction(data) {
  return request({
    url: '/system/billing/transaction',
    method: 'put',
    data: data
  })
}

// 删除会员消费/充值流水
export function delBillingTransaction(id) {
  return request({
    url: '/system/billing/transaction/' + id,
    method: 'delete'
  })
}
