import request from '@/utils/request'

// 查询套餐订单列表
export function listBillingOrder(query) {
  return request({
    url: '/system/billing/order/list',
    method: 'get',
    params: query
  })
}

// 查询套餐订单详细
export function getBillingOrder(id) {
  return request({
    url: '/system/billing/order/' + id,
    method: 'get'
  })
}

// 新增套餐订单
export function addBillingOrder(data) {
  return request({
    url: '/system/billing/order',
    method: 'post',
    data: data
  })
}

// 修改套餐订单
export function updateBillingOrder(data) {
  return request({
    url: '/system/billing/order',
    method: 'put',
    data: data
  })
}

// 删除套餐订单
export function delBillingOrder(id) {
  return request({
    url: '/system/billing/order/' + id,
    method: 'delete'
  })
}
