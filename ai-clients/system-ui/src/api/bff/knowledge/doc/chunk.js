import request from '@/utils/request'

// 查询文档分块列表
export function listKnowledgeDocChunk(query) {
  return request({
    url: '/system/knowledgeDocChunk/list',
    method: 'get',
    params: query
  })
}

// 查询文档分块详细
export function getKnowledgeDocChunk(id) {
  return request({
    url: '/system/knowledgeDocChunk/' + id,
    method: 'get'
  })
}

// 新增文档分块
export function addKnowledgeDocChunk(data) {
  return request({
    url: '/system/knowledgeDocChunk',
    method: 'post',
    data: data
  })
}

// 修改文档分块
export function updateKnowledgeDocChunk(data) {
  return request({
    url: '/system/knowledgeDocChunk',
    method: 'put',
    data: data
  })
}

// 删除文档分块
export function delKnowledgeDocChunk(id) {
  return request({
    url: '/system/knowledgeDocChunk/' + id,
    method: 'delete'
  })
}
