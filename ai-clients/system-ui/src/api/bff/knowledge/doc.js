import request from '@/utils/request'

// 查询知识库文档列表
export function listKnowledgeDoc(query) {
  return request({
    url: '/system/knowledgeDoc/list',
    method: 'get',
    params: query
  })
}

// 查询知识库文档详细
export function getKnowledgeDoc(id) {
  return request({
    url: '/system/knowledgeDoc/' + id,
    method: 'get'
  })
}

// 新增知识库文档
export function addKnowledgeDoc(data) {
  return request({
    url: '/system/knowledgeDoc',
    method: 'post',
    data: data
  })
}

// 修改知识库文档
export function updateKnowledgeDoc(data) {
  return request({
    url: '/system/knowledgeDoc',
    method: 'put',
    data: data
  })
}

// 删除知识库文档
export function delKnowledgeDoc(id) {
  return request({
    url: '/system/knowledgeDoc/' + id,
    method: 'delete'
  })
}
