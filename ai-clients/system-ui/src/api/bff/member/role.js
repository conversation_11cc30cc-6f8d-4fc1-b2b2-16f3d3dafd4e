import request from '@/utils/request'

// 查询会员角色列表
export function listMemberRole(query) {
  return request({
    url: '/system/member/roles/list',
    method: 'get',
    params: query
  })
}

// 查询会员角色详细
export function getMemberRole(id) {
  return request({
    url: '/system/member/roles/' + id,
    method: 'get'
  })
}

// 新增会员角色
export function addMemberRole(data) {
  return request({
    url: '/system/member/roles',
    method: 'post',
    data: data
  })
}

// 修改会员角色
export function updateMemberRole(data) {
  return request({
    url: '/system/member/roles',
    method: 'put',
    data: data
  })
}

// 删除会员角色
export function delMemberRole(id) {
  return request({
    url: '/system/member/roles/' + id,
    method: 'delete'
  })
}
