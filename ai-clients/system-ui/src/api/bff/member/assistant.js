import request from '@/utils/request'

// 查询会员助手列表
export function listMemberAssistant(query) {
  return request({
    url: '/system/member/assistant/list',
    method: 'get',
    params: query
  })
}

// 查询会员助手详细
export function getMemberAssistant(id) {
  return request({
    url: '/system/member/assistant/' + id,
    method: 'get'
  })
}

// 新增会员助手
export function addMemberAssistant(data) {
  return request({
    url: '/system/member/assistant',
    method: 'post',
    data: data
  })
}

// 修改会员助手
export function updateMemberAssistant(data) {
  return request({
    url: '/system/member/assistant',
    method: 'put',
    data: data
  })
}

// 删除会员助手
export function delMemberAssistant(id) {
  return request({
    url: '/system/member/assistant/' + id,
    method: 'delete'
  })
}
