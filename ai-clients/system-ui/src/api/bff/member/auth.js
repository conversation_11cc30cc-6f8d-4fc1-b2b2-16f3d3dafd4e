import request from '@/utils/request'

// 查询会员认证信息列表
export function listMemberAuth(query) {
  return request({
    url: '/system/member/auth/list',
    method: 'get',
    params: query
  })
}

// 查询会员认证信息详细
export function getMemberAuth(id) {
  return request({
    url: '/system/member/auth/' + id,
    method: 'get'
  })
}

// 新增会员认证信息
export function addMemberAuth(data) {
  return request({
    url: '/system/member/auth',
    method: 'post',
    data: data
  })
}

// 修改会员认证信息
export function updateMemberAuth(data) {
  return request({
    url: '/system/member/auth',
    method: 'put',
    data: data
  })
}

// 删除会员认证信息
export function delMemberAuth(id) {
  return request({
    url: '/system/member/auth/' + id,
    method: 'delete'
  })
}
