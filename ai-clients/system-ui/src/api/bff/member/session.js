import request from '@/utils/request'

// 查询会话信息列表
export function listMemberSession(query) {
  return request({
    url: '/system/member/session/list',
    method: 'get',
    params: query
  })
}

// 查询会话信息详细
export function getMemberSession(id) {
  return request({
    url: '/system/member/session/' + id,
    method: 'get'
  })
}

// 新增会话信息
export function addMemberSession(data) {
  return request({
    url: '/system/member/session',
    method: 'post',
    data: data
  })
}

// 修改会话信息
export function updateMemberSession(data) {
  return request({
    url: '/system/member/session',
    method: 'put',
    data: data
  })
}

// 删除会话信息
export function delMemberSession(id) {
  return request({
    url: '/system/member/session/' + id,
    method: 'delete'
  })
}
