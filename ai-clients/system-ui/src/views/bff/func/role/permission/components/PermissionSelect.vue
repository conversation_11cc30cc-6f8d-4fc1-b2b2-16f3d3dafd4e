<template>
  <el-dialog append-to-body :title="title" v-model="dialogVisible" width="800px" @close="handleClose">
    <el-form :inline="true">
      <el-form-item label="权限名称">
        <el-input
          v-model="permissionFilterName"
          placeholder="请输入权限名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="权限标识">
        <el-input
          v-model="permissionFilterCode"
          placeholder="请输入权限标识"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchPermissions">搜索</el-button>
        <el-button @click="resetPermissionFilter">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-table
      v-loading="permissionLoading"
      :data="permissionList"
      @row-click="handlePermissionRowClick"
      highlight-current-row
      style="width: 100%"
      ref="permissionTableRef"
      :row-class-name="rowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" v-if="multiple" />
      <el-table-column label="权限名称" prop="name" />
      <el-table-column label="权限标识" prop="code" />
      <el-table-column label="权限ID" prop="id" width="80" />
    </el-table>
    
    <pagination
      v-show="permissionTotal > 0"
      :total="permissionTotal"
      v-model:page="permissionQueryParams.pageNum"
      v-model:limit="permissionQueryParams.pageSize"
      @pagination="getPermissionList"
    />
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmSelectPermission" type="primary">确 定</el-button>
        <el-button @click="cancelSelectPermission">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, nextTick } from 'vue'
import { listFuncPermission, getFuncPermission } from "@/api/bff/func/permission"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择权限'
  },
  selectedId: {
    type: [String, Number, Array],
    default: null
  },
  multiple: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'select', 'cancel'])

// 权限相关
const dialogVisible = ref(props.visible)
const permissionList = ref([])
const permissionLoading = ref(false)
const permissionTotal = ref(0)
const currentPermissionId = ref(props.selectedId)
const selectedPermissions = ref([]) // 多选模式下选中的权限
const permissionMap = ref({})
const permissionFilterName = ref('')
const permissionFilterCode = ref('')
const permissionTableRef = ref(null)
const permissionQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  code: ''
})

// 监听props变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    resetPermissionFilter()
    getPermissionList()
  }
})

watch(() => props.selectedId, (val) => {
  currentPermissionId.value = val
  
  // 多选模式下，初始化选中项
  if (props.multiple && Array.isArray(val)) {
    selectedPermissions.value = [...val]
  }
})

// 设置行的class
const rowClassName = ({ row }) => {
  if (!props.multiple && row.id === currentPermissionId.value) {
    return 'selected-row'
  }
  return ''
}

// 获取权限列表
const getPermissionList = () => {
  permissionLoading.value = true
  permissionQueryParams.name = permissionFilterName.value
  permissionQueryParams.code = permissionFilterCode.value
  
  listFuncPermission(permissionQueryParams).then(response => {
    permissionList.value = response.rows
    permissionTotal.value = response.total
    
    // 构建权限映射
    buildPermissionMap(response.rows)
    
    permissionLoading.value = false
    
    // 如果有选中项，确保表格渲染完成后设置当前行高亮
    nextTick(() => {
      if (props.multiple && Array.isArray(currentPermissionId.value)) {
        // 多选模式，设置选中的行
        currentPermissionId.value.forEach(id => {
          const row = permissionList.value.find(item => item.id === id)
          if (row) {
            permissionTableRef.value?.toggleRowSelection(row, true)
          }
        })
      } else if (!props.multiple && currentPermissionId.value) {
        // 单选模式，设置当前行
        const selectedRow = permissionList.value.find(item => item.id === currentPermissionId.value)
        if (selectedRow) {
          permissionTableRef.value?.setCurrentRow(selectedRow)
        }
      }
    })
  })
}

// 搜索权限
const searchPermissions = () => {
  permissionQueryParams.pageNum = 1
  getPermissionList()
}

// 重置权限过滤条件
const resetPermissionFilter = () => {
  permissionFilterName.value = ''
  permissionFilterCode.value = ''
  permissionQueryParams.pageNum = 1
  permissionQueryParams.name = ''
  permissionQueryParams.code = ''
}

// 构建权限映射
const buildPermissionMap = (permissions) => {
  permissions.forEach(permission => {
    permissionMap.value[permission.id] = permission.name
  })
}

// 权限行点击事件
const handlePermissionRowClick = (row) => {
  if (props.multiple) {
    // 多选模式下，点击行切换选中状态
    permissionTableRef.value?.toggleRowSelection(row)
  } else {
    // 单选模式
    currentPermissionId.value = row.id
    permissionTableRef.value?.setCurrentRow(row)
  }
}

// 表格选择变化事件
const handleSelectionChange = (selection) => {
  selectedPermissions.value = selection.map(item => item.id)
}

// 确认选择权限
const confirmSelectPermission = () => {
  if (props.multiple) {
    // 多选模式
    const selectedIds = selectedPermissions.value
    const selectedNames = selectedIds.map(id => permissionMap.value[id] || id)
    
    emit('select', {
      ids: selectedIds,
      names: selectedNames
    })
  } else if (currentPermissionId.value !== undefined) {
    // 单选模式
    const selectedName = permissionMap.value[currentPermissionId.value] || currentPermissionId.value
    emit('select', {
      id: currentPermissionId.value,
      name: selectedName
    })
  }
  handleClose()
}

// 取消选择权限
const cancelSelectPermission = () => {
  emit('cancel')
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 初始化
if (props.visible) {
  getPermissionList()
}
</script>

<style scoped>
:deep(.selected-row) {
  background-color: #f0f9eb;
}
</style> 