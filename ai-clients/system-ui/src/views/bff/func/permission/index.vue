<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="权限名称" prop="name">
        <el-input
                @keyup.enter="handleQuery"
                clearable
                placeholder="请输入权限名称"
                v-model="queryParams.name"
        />
      </el-form-item>
      <el-form-item label="权限标识" prop="code">
        <el-input
                @keyup.enter="handleQuery"
                clearable
                placeholder="请输入权限标识"
                v-model="queryParams.code"
        />
      </el-form-item>
      <el-form-item label="权限组" prop="groupId">
        <el-input
                @click="openGroupSelectDialog"
                placeholder="请选择权限组"
                readonly
                v-model="groupName"
        >
          <template #append>
            <el-button @click.stop="openGroupSelectDialog">
              <el-icon>
                <Search/>
              </el-icon>
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <!--
      <el-form-item label="权限ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入权限ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属功能" prop="module">
        <el-input
          v-model="queryParams.module"
          placeholder="请输入所属功能"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:funcPermission:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:funcPermission:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:funcPermission:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:funcPermission:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="funcPermissionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :show-overflow-tooltip="true" label="权限名称" prop="name"/>
      <el-table-column :show-overflow-tooltip="true" label="权限标识" prop="code"/>
      <!--<el-table-column label="权限ID" prop="id" width="80"/>-->
      <el-table-column label="权限组" prop="groupId" width="80">
        <template #default="scope">
          <span>{{ getGroupName(scope.row.groupId) }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column :show-overflow-tooltip="true" label="所属功能" prop="module"/>-->
      <el-table-column align="center" label="是否内置" prop="isSystem" width="80"/>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:funcPermission:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:funcPermission:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改权限定义对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="funcPermissionRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="权限组" prop="groupId">
          <el-input
                  @click="openFormGroupSelectDialog"
                  placeholder="请选择权限组"
                  readonly
                  v-model="formGroupName"
          >
            <template #append>
              <el-button @click.stop="openFormGroupSelectDialog">
                <el-icon>
                  <Search/>
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input placeholder="请输入权限名称" v-model="form.name"/>
        </el-form-item>
        <el-form-item label="权限标识" prop="code">
          <el-input v-model="form.code" placeholder="请输入权限标识" />
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <!--<el-form-item label="所属功能" prop="module">
          <el-input v-model="form.module" placeholder="请输入所属功能" />
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限组选择对话框 -->
    <el-dialog append-to-body title="选择权限组" v-model="groupSelectOpen" width="500px">
      <el-input
              clearable
              placeholder="请输入关键字进行过滤"
              style="margin-bottom: 10px"
              v-model="groupFilterText"
      />
      <el-tree
              :data="groupTreeData"
              :filter-node-method="filterNode"
              :props="{ label: 'name', children: 'children' }"
              @node-click="handleNodeClick"
              highlight-current
              node-key="id"
              ref="groupTreeRef"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="confirmSelectGroup" type="primary">确 定</el-button>
          <el-button @click="cancelSelectGroup">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FuncPermission">
import { listFuncPermission, getFuncPermission, delFuncPermission, addFuncPermission, updateFuncPermission } from "@/api/bff/func/permission"
import { listFuncPermissionGroup, getFuncPermissionGroup } from "@/api/bff/func/permission/group"
import { useRoute } from 'vue-router'
import { Search } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const route = useRoute()

const funcPermissionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

// 权限组相关
const groupName = ref('')
const formGroupName = ref('')
const groupSelectOpen = ref(false)
const groupTreeData = ref([])
const groupFilterText = ref('')
const groupTreeRef = ref(null)
const currentGroupId = ref(null)
const groupMap = ref({})
const isFormGroupSelect = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    groupId: null,
    code: null,
    name: null,
    module: null,
    isSystem: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  },
  rules: {
    name: [
      { required: true, message: "权限名称不能为空", trigger: "blur" }
    ],
    code: [
      { required: true, message: "权限标识不能为空", trigger: "blur" }
    ],
    groupId: [
      { required: true, message: "权限组不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 监听过滤文本变化
watch(groupFilterText, (val) => {
  groupTreeRef.value?.filter(val)
})

// 过滤节点方法
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.indexOf(value) !== -1
}

// 打开权限组选择对话框 - 查询条件
const openGroupSelectDialog = () => {
  isFormGroupSelect.value = false
  getGroupTreeData()
  groupSelectOpen.value = true
}

// 打开权限组选择对话框 - 表单
const openFormGroupSelectDialog = () => {
  isFormGroupSelect.value = true
  getGroupTreeData()
  groupSelectOpen.value = true
}

// 获取权限组树形数据
const getGroupTreeData = () => {
  listFuncPermissionGroup().then(response => {
    const data = { id: null, name: '顶级节点', children: [] }
    data.children = proxy.handleTree(response.data, "id", "pid")
    groupTreeData.value = [data]
    
    // 构建groupMap用于显示名称
    buildGroupMap(response.data)
    
    // 如果有初始groupId，获取其名称
    if (queryParams.value.groupId && !groupName.value) {
      getGroupNameById(queryParams.value.groupId)
    }
  })
}

// 构建权限组映射
const buildGroupMap = (groups) => {
  groups.forEach(group => {
    groupMap.value[group.id] = group.name
  })
}

// 根据ID获取权限组名称
const getGroupNameById = (id) => {
  if (!id) return ''
  
  if (groupMap.value[id]) {
    groupName.value = groupMap.value[id]
    return groupMap.value[id]
  }
  
  // 如果map中没有，尝试从后端获取
  getFuncPermissionGroup(id).then(response => {
    if (response.data) {
      groupMap.value[id] = response.data.name
      groupName.value = response.data.name
    }
  })
  
  return id
}

// 获取表格中权限组名称
const getGroupName = (id) => {
  return groupMap.value[id] || id
}

// 树节点点击事件
const handleNodeClick = (data) => {
  currentGroupId.value = data.id
}

// 确认选择权限组
const confirmSelectGroup = () => {
  if (currentGroupId.value !== undefined) {
    if (isFormGroupSelect.value) {
      // 表单中的选择
      form.value.groupId = currentGroupId.value
      formGroupName.value = groupMap.value[currentGroupId.value] || currentGroupId.value
    } else {
      // 查询条件中的选择
      queryParams.value.groupId = currentGroupId.value
      groupName.value = groupMap.value[currentGroupId.value] || currentGroupId.value
    }
  }
  groupSelectOpen.value = false
}

// 取消选择权限组
const cancelSelectGroup = () => {
  groupSelectOpen.value = false
}

// 检查路由参数中是否有groupId
const initRouteParams = () => {
  if (route.params.groupId) {
    queryParams.value.groupId = route.params.groupId
    getGroupNameById(route.params.groupId)
  }
  if (route.query.groupId) {
    queryParams.value.groupId = route.query.groupId
    getGroupNameById(route.query.groupId)
  }
}

/** 查询权限定义列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listFuncPermission(queryParams.value).then(response => {
    funcPermissionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    groupId: null,
    code: null,
    name: null,
    description: null,
    module: null,
    isSystem: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  formGroupName.value = ''
  proxy.resetForm("funcPermissionRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  // 清空权限组
  queryParams.value.groupId = null
  groupName.value = ''
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加权限定义"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getFuncPermission(_id).then(response => {
    form.value = response.data
    // 设置表单中的权限组名称
    if (form.value.groupId) {
      formGroupName.value = groupMap.value[form.value.groupId] || form.value.groupId
    }
    open.value = true
    title.value = "修改权限定义"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["funcPermissionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateFuncPermission(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addFuncPermission(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除权限定义编号为"' + _ids + '"的数据项？').then(function() {
    return delFuncPermission(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/func/permission/export', {
    ...queryParams.value
  }, `funcPermission_${new Date().getTime()}.xlsx`)
}

// 初始化
getGroupTreeData()
initRouteParams()
getList()
</script>
