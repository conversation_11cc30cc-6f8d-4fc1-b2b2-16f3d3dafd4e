<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐所有者" prop="ownerMemberId">
        <el-input
          v-model="queryParams.ownerMemberId"
          placeholder="请输入套餐所有者"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入套餐名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择套餐类型" clearable>
          <el-option
            v-for="dict in package_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="套餐标价" prop="price">
        <el-input
          v-model="queryParams.price"
          placeholder="请输入套餐标价"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货币" prop="currencyId">
        <el-input
          v-model="queryParams.currencyId"
          placeholder="请输入货币"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点数" prop="creditsGranted">
        <el-input
          v-model="queryParams.creditsGranted"
          placeholder="请输入点数"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点数有效期" prop="validityDays">
        <el-input
          v-model="queryParams.validityDays"
          placeholder="请输入点数有效期"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="续订单位" prop="renewalIntervalUnit">
        <el-select v-model="queryParams.renewalIntervalUnit" placeholder="请选择续订单位" clearable>
          <el-option
            v-for="dict in renewal_interval_unit"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="会员等级" prop="memberLevelGrant">
        <el-select v-model="queryParams.memberLevelGrant" placeholder="请选择会员等级" clearable>
          <el-option
            v-for="dict in member_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="套餐状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择套餐状态" clearable>
          <el-option
            v-for="dict in package_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:billingPackage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:billingPackage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:billingPackage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:billingPackage:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="billingPackageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="套餐所有者" align="center" prop="ownerMemberId" />
      <el-table-column label="套餐名称" align="center" prop="name" />
      <el-table-column label="套餐类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :options="package_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="套餐标价" align="center" prop="price" />
      <el-table-column label="货币" align="center" prop="currencyId" />
      <el-table-column label="点数" align="center" prop="creditsGranted" />
      <el-table-column label="点数有效期" align="center" prop="validityDays" />
      <el-table-column label="续订周期" align="center" prop="renewalInterval" />
      <el-table-column label="续订单位" align="center" prop="renewalIntervalUnit">
        <template #default="scope">
          <dict-tag :options="renewal_interval_unit" :value="scope.row.renewalIntervalUnit"/>
        </template>
      </el-table-column>
      <el-table-column label="会员等级" align="center" prop="memberLevelGrant">
        <template #default="scope">
          <dict-tag :options="member_level" :value="scope.row.memberLevelGrant"/>
        </template>
      </el-table-column>
      <el-table-column label="套餐状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="package_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:billingPackage:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:billingPackage:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计费套餐对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="billingPackageRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="套餐所有者" prop="ownerMemberId">
          <el-input v-model="form.ownerMemberId" placeholder="请输入套餐所有者" />
        </el-form-item>
        <el-form-item label="套餐标识" prop="code">
          <el-input v-model="form.code" placeholder="请输入套餐标识" />
        </el-form-item>
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="套餐类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择套餐类型">
            <el-option
              v-for="dict in package_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="套餐标价" prop="price">
          <el-input v-model="form.price" placeholder="请输入套餐标价" />
        </el-form-item>
        <el-form-item label="货币" prop="currencyId">
          <el-input v-model="form.currencyId" placeholder="请输入货币" />
        </el-form-item>
        <el-form-item label="点数" prop="creditsGranted">
          <el-input v-model="form.creditsGranted" placeholder="请输入点数" />
        </el-form-item>
        <el-form-item label="点数有效期" prop="validityDays">
          <el-input v-model="form.validityDays" placeholder="请输入点数有效期" />
        </el-form-item>
        <el-form-item label="续订单位" prop="renewalIntervalUnit">
          <el-select v-model="form.renewalIntervalUnit" placeholder="请选择续订单位">
            <el-option
              v-for="dict in renewal_interval_unit"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会员等级" prop="memberLevelGrant">
          <el-select v-model="form.memberLevelGrant" placeholder="请选择会员等级">
            <el-option
              v-for="dict in member_level"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="套餐状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择套餐状态">
            <el-option
              v-for="dict in package_status"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BillingPackage">
import { listBillingPackage, getBillingPackage, delBillingPackage, addBillingPackage, updateBillingPackage } from "@/api/bff/billing/package"

const { proxy } = getCurrentInstance()
const { package_status, renewal_interval_unit, package_type, member_level } = proxy.useDict('package_status', 'renewal_interval_unit', 'package_type', 'member_level')

const billingPackageList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    ownerMemberId: null,
    name: null,
    type: null,
    price: null,
    currencyId: null,
    creditsGranted: null,
    validityDays: null,
    renewalInterval: null,
    renewalIntervalUnit: null,
    memberLevelGrant: null,
    status: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询计费套餐列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listBillingPackage(queryParams.value).then(response => {
    billingPackageList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    ownerMemberId: null,
    code: null,
    name: null,
    description: null,
    type: null,
    price: null,
    currencyId: null,
    creditsGranted: null,
    validityDays: null,
    renewalInterval: null,
    renewalIntervalUnit: null,
    memberLevelGrant: null,
    status: null,
    sortOrder: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("billingPackageRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加计费套餐"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getBillingPackage(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改计费套餐"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["billingPackageRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateBillingPackage(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addBillingPackage(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除计费套餐编号为"' + _ids + '"的数据项？').then(function() {
    return delBillingPackage(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/billing/package/export', {
    ...queryParams.value
  }, `billingPackage_${new Date().getTime()}.xlsx`)
}

getList()
</script>
