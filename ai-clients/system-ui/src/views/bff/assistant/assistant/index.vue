<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="助手ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入助手ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-input
          v-model="queryParams.categoryId"
          placeholder="请输入分类"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所有者" prop="ownerMemberId">
        <el-input
          v-model="queryParams.ownerMemberId"
          placeholder="请输入所有者"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所有者分成" prop="revenueShareRate">
        <el-input
          v-model="queryParams.revenueShareRate"
          placeholder="请输入所有者分成"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入助手编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入助手名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="付费套餐" prop="billingPackageIds">
        <el-input
          v-model="queryParams.billingPackageIds"
          placeholder="请输入付费套餐"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="知识库" prop="knowledgeIds">
        <el-input
          v-model="queryParams.knowledgeIds"
          placeholder="请输入知识库"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="交互模式" prop="interactionMode">
        <el-input
          v-model="queryParams.interactionMode"
          placeholder="请输入交互模式"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手状态 " prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择助手状态 " clearable>
          <el-option
            v-for="dict in assistant_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="使用次数" prop="usageCount">
        <el-input
          v-model="queryParams.usageCount"
          placeholder="请输入使用次数"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:assistant:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:assistant:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:assistant:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:assistant:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assistantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="助手ID" align="center" prop="id" />
      <el-table-column label="分类" align="center" prop="categoryId" />
      <el-table-column label="所有者" align="center" prop="ownerMemberId" />
      <el-table-column label="所有者分成" align="center" prop="revenueShareRate" />
      <el-table-column label="助手编码" align="center" prop="code" />
      <el-table-column label="助手名称" align="center" prop="name" />
      <el-table-column label="付费套餐" align="center" prop="billingPackageIds" />
      <el-table-column label="知识库" align="center" prop="knowledgeIds" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="图标" align="center" prop="iconUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.iconUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="交互模式" align="center" prop="interactionMode">
        <template #default="scope">
          <dict-tag :options="assistant_interaction_mode" :value="scope.row.interactionMode"/>
        </template>
      </el-table-column>
      <el-table-column label="助手状态 " align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="assistant_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="使用次数" align="center" prop="usageCount" />
      <el-table-column label="是否公开" align="center" prop="isPublic" />
      <el-table-column label="是否预设" align="center" prop="isPreset" />
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:assistant:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:assistant:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改助手定义对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="assistantRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="分类" prop="categoryId">
          <el-input v-model="form.categoryId" placeholder="请输入分类" />
        </el-form-item>
        <el-form-item label="所有者" prop="ownerMemberId">
          <el-input v-model="form.ownerMemberId" placeholder="请输入所有者" />
        </el-form-item>
        <el-form-item label="所有者分成" prop="revenueShareRate">
          <el-input v-model="form.revenueShareRate" placeholder="请输入所有者分成" />
        </el-form-item>
        <el-form-item label="助手编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入助手编码" />
        </el-form-item>
        <el-form-item label="助手名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入助手名称" />
        </el-form-item>
        <el-form-item label="付费套餐" prop="billingPackageIds">
          <el-input v-model="form.billingPackageIds" placeholder="请输入付费套餐" />
        </el-form-item>
        <el-form-item label="知识库" prop="knowledgeIds">
          <el-input v-model="form.knowledgeIds" placeholder="请输入知识库" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图标" prop="iconUrl">
          <image-upload v-model="form.iconUrl"/>
        </el-form-item>
        <el-form-item label="提示词" prop="promptTemplate">
          <el-input v-model="form.promptTemplate" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="交互模式" prop="interactionMode">
          <el-input v-model="form.interactionMode" placeholder="请输入交互模式" />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="助手状态 " prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in assistant_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="使用次数" prop="usageCount">
          <el-input v-model="form.usageCount" placeholder="请输入使用次数" />
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Assistant">
import { listAssistant, getAssistant, delAssistant, addAssistant, updateAssistant } from "@/api/bff/assistant/assistant"

const { proxy } = getCurrentInstance()
const { assistant_status } = proxy.useDict('assistant_status')

const assistantList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    categoryId: null,
    ownerMemberId: null,
    revenueShareRate: null,
    code: null,
    name: null,
    billingPackageIds: null,
    knowledgeIds: null,
    description: null,
    interactionMode: null,
    status: null,
    usageCount: null,
    isPublic: null,
    isPreset: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询助手定义列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listAssistant(queryParams.value).then(response => {
    assistantList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    categoryId: null,
    ownerMemberId: null,
    revenueShareRate: null,
    code: null,
    name: null,
    billingPackageIds: null,
    knowledgeIds: null,
    description: null,
    iconUrl: null,
    promptTemplate: null,
    interactionMode: null,
    modelSuggestions: null,
    version: null,
    status: null,
    usageCount: null,
    isPublic: null,
    isPreset: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("assistantRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加助手定义"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getAssistant(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改助手定义"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["assistantRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAssistant(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addAssistant(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除助手定义编号为"' + _ids + '"的数据项？').then(function() {
    return delAssistant(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assistant/assistants/export', {
    ...queryParams.value
  }, `assistant_${new Date().getTime()}.xlsx`)
}

getList()
</script>
