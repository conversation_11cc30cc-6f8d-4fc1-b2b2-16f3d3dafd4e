<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="会员助手ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入会员助手ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员ID" prop="memberId">
        <el-input
          v-model="queryParams.memberId"
          placeholder="请输入会员ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手ID" prop="assistantId">
        <el-input
          v-model="queryParams.assistantId"
          placeholder="请输入助手ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手版本" prop="templateVersion">
        <el-input
          v-model="queryParams.templateVersion"
          placeholder="请输入助手版本"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手模型" prop="modelId">
        <el-input
          v-model="queryParams.modelId"
          placeholder="请输入助手模型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助手呢称" prop="customName">
        <el-input
          v-model="queryParams.customName"
          placeholder="请输入助手呢称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:memberAssistant:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:memberAssistant:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:memberAssistant:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:memberAssistant:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberAssistantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会员助手ID" align="center" prop="id" />
      <el-table-column label="会员ID" align="center" prop="memberId" />
      <el-table-column label="助手ID" align="center" prop="assistantId" />
      <el-table-column label="助手版本" align="center" prop="templateVersion" />
      <el-table-column label="助手模型" align="center" prop="modelId" />
      <el-table-column label="助手呢称" align="center" prop="customName" />
      <el-table-column label="是否收藏" align="center" prop="isFavorite" />
      <el-table-column label="是否启用" align="center" prop="isActive" />
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:memberAssistant:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:memberAssistant:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员助手对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="memberAssistantRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="会员ID" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员ID" />
        </el-form-item>
        <el-form-item label="助手ID" prop="assistantId">
          <el-input v-model="form.assistantId" placeholder="请输入助手ID" />
        </el-form-item>
        <el-form-item label="助手版本" prop="templateVersion">
          <el-input v-model="form.templateVersion" placeholder="请输入助手版本" />
        </el-form-item>
        <el-form-item label="助手模型" prop="modelId">
          <el-input v-model="form.modelId" placeholder="请输入助手模型" />
        </el-form-item>
        <el-form-item label="助手呢称" prop="customName">
          <el-input v-model="form.customName" placeholder="请输入助手呢称" />
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MemberAssistant">
import { listMemberAssistant, getMemberAssistant, delMemberAssistant, addMemberAssistant, updateMemberAssistant } from "@/api/bff/member/assistant"

const { proxy } = getCurrentInstance()

const memberAssistantList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    memberId: null,
    assistantId: null,
    templateVersion: null,
    modelId: null,
    customName: null,
    settingsOverride: null,
    isFavorite: null,
    isActive: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询会员助手列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listMemberAssistant(queryParams.value).then(response => {
    memberAssistantList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    memberId: null,
    assistantId: null,
    templateVersion: null,
    modelId: null,
    customName: null,
    settingsOverride: null,
    isFavorite: null,
    isActive: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("memberAssistantRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会员助手"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getMemberAssistant(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会员助手"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["memberAssistantRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateMemberAssistant(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addMemberAssistant(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除会员助手编号为"' + _ids + '"的数据项？').then(function() {
    return delMemberAssistant(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/member/assistant/export', {
    ...queryParams.value
  }, `memberAssistant_${new Date().getTime()}.xlsx`)
}

getList()
</script>
