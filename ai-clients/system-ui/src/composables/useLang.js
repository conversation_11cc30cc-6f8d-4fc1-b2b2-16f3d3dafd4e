import { ref } from 'vue'
import { listBaseLang } from '@/api/bff/base/lang'

export const langList = ref([])

function getLangList() {
  const defaultParams = { pageNum: 1, pageSize: 1000 }
  return listBaseLang(defaultParams)
}

// Data will be fetched when the module is first imported.
getLangList().then(response => {
  langList.value = response.rows
})

export const langIdToName = (langId) => {
  const lang = langList.value.find(item => item.id === langId)
  return lang ? lang.name : ''
} 