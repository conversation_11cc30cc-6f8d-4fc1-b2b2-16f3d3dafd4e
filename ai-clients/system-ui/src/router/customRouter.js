// 自定义路由配置
export const customRoutes = [
  {
    path: '/bff/func/permission',
    component: 'Layout',
    hidden: true,
    permissions: ['system:funcPermission:list'],
    children: [
      {
        path: '',
        component: () => import('@/views/bff/func/permission/index'),
        name: 'FuncPermission',
        meta: { title: '权限列表', activeMenu: '/bff/func/permission/group' }
      }
    ]
  },
  {
    path: '/bff/func/role/permission',
    component: 'Layout',
    hidden: true,
    permissions: ['system:funcRolePermission:list'],
    children: [
      {
        path: '',
        component: () => import('@/views/bff/func/role/permission/index'),
        name: 'FuncRolePermission',
        meta: { title: '角色权限列表', activeMenu: '/bff/func/role' }
      }
    ]
  }
]

export default customRoutes
