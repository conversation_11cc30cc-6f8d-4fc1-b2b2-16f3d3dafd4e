package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;

/**
 * 知识库简要视图对象
 * <p>
 * 用于向前端展示知识库基本信息，包含统计数据。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@EqualsAndHashCode(of = "id")
public class KnowledgeBaseVo {

    /**
     * 知识库ID
     */
    private Long id;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 排序值
     */
    private Integer sortOrder;

    /**
     * 权限类型 (1-私有, 2-租户共享)
     */
    private Integer permissionType;

    /**
     * 文档数量
     */
    private Integer docCount;

    /**
     * 总字符数
     */
    private Long totalCharCount;

    /**
     * 创建时间
     */
    private OffsetDateTime createTime;

    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
}