package ai.showlab.bff.mapper.v1.model;

import ai.showlab.bff.entity.domain.v1.model.ModelOutputFormat;
import ai.showlab.bff.entity.vo.v1.ModelOutputFormatVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型输出格式 数据层
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Repository
public interface ModelOutputFormatMapper {

    /**
     * 根据模型ID查询其支持的所有输出格式
     *
     * @param modelId 模型ID
     * @return 模型输出格式列表
     */
    List<ModelOutputFormat> selectModelOutputFormatsByModelId(Long modelId);

    /**
     * 根据模型ID查询其支持的所有输出格式VO
     *
     * @param modelId 模型ID
     * @return 模型输出格式VO列表
     */
    List<ModelOutputFormatVo> selectModelOutputFormatVosByModelId(Long modelId);
} 