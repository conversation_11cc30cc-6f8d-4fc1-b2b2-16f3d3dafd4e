package ai.showlab.bff.mapper.v1.model;

import ai.showlab.bff.entity.domain.v1.model.ModelCategory;
import ai.showlab.bff.entity.vo.v1.ModelCategoryVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型分类 数据层
 * <p>
 * 提供模型分类的基础数据库操作。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Repository
public interface ModelCategoryMapper {

    /**
     * 根据ID查询模型分类
     *
     * @param id 分类ID
     * @return 模型分类对象
     */
    ModelCategory selectModelCategoryById(Long id);

    /**
     * 根据编码查询模型分类
     *
     * @param code 分类编码
     * @return 模型分类对象
     */
    ModelCategory selectModelCategoryByCode(String code);

    /**
     * 查询所有模型分类
     *
     * @return 模型分类列表
     */
    List<ModelCategory> selectAllModelCategories();

    /**
     * 查询所有模型分类VO（直接返回VO）
     *
     * @return 模型分类VO列表
     */
    List<ModelCategoryVo> selectAllModelCategoriesVo();
} 