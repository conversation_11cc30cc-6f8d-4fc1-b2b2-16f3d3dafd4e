package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.docs.MemberApiAnnotations;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.bo.LoginMember;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.MemberInfoVo;
import ai.showlab.bff.service.common.IDuplicateSubmissionTokenService;
import ai.showlab.bff.service.v1.member.IMemberAuthService;
import ai.showlab.bff.service.v1.member.IMemberService;
import ai.showlab.bff.service.v1.member.IMemberSessionService;
import ai.showlab.common.core.web.domain.RestResult;
import com.ruoyi.common.core.utils.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员接口 (面向外部普通用户)
 * 封装了用户相关的认证、个人信息等操作
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/member")
@Tag(name = "会员接口", description = "提供了普通会员的用户认证、个人信息管理等核心功能。")
public class MemberController extends BaseController {

    @Autowired
    private IMemberAuthService memberAuthService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberSessionService memberSessionService;
    @Autowired
    private IDuplicateSubmissionTokenService duplicateSubmissionTokenService;

    /**
     * 用户注册
     * @param requestParams 通用请求参数
     * @return 注册结果
     */
    @MemberApiAnnotations.RegisterApiDoc()
    // TODO：防止重复提交
    //@PreventDuplicateSubmission(keyPrefix = "register")
    @ApiParamValidate(bizParamClass = MemberRegisterParam.class)
    @PostMapping("/register")
    public ResponseEntity<RestResult> register(RequestParams<MemberRegisterParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            MemberRegisterParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 业务校验
            if (StringUtils.isBlank(param.getUsername()) && StringUtils.isBlank(param.getEmail()) && StringUtils.isBlank(param.getPhone())) {
                throw new BusinessException("用户名、邮箱和手机号至少需要提供一个");
            }

            // 直接传递 requestParams 到 service 层
            memberAuthService.register(requestParams);
            return RestResult.ok("注册成功");
        }, "注册失败，请稍后重试");
    }

    /**
     * 用户登录
     * @param requestParams 通用请求参数（已包含HttpServletRequest）
     * @return 登录结果，包含token等信息
     */
    @MemberApiAnnotations.LoginApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "login")
    @ApiParamValidate(bizParamClass = MemberLoginParam.class)
    @PostMapping("/login")
    public ResponseEntity<RestResult> login(RequestParams<MemberLoginParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            MemberLoginParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 调用登录服务（RequestParams中已包含HttpServletRequest）
            String token = memberAuthService.login(requestParams);
            return RestResult.ok("登录成功", token);
        }, "登录失败，请稍后重试");
    }

    /**
     * 获取当前登录用户信息
     * @return 用户信息
     */
    @MemberApiAnnotations.GetMemberInfoApiDoc()
    @PostMapping("/profile")
    public ResponseEntity<RestResult> getProfile() {
        return executeWithTryCatch(() -> {
            MemberInfoVo memberInfo = memberService.getMemberInfoVo(BffKit.getCurrentMemberId());
            return checkNotNull(memberInfo, "用户信息不存在");
        }, "获取用户信息失败，请稍后重试");
    }

    /**
     * 更新用户昵称
     * @param requestParams 通用请求参数
     * @return 更新结果
     */
    @MemberApiAnnotations.UpdateNicknameApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "updateNickname") // 防止重复提交
    @ApiParamValidate(bizParamClass = UpdateNicknameParam.class)
    @PostMapping("/update/nickname")
    public ResponseEntity<RestResult> updateNickname(RequestParams<UpdateNicknameParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            UpdateNicknameParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 直接传递 requestParams 到 service 层
            memberService.updateMemberNickname(requestParams);
            return RestResult.ok("昵称更新成功");
        }, "昵称更新失败，请稍后重试");
    }

    /**
     * 用户登出
     * @return 登出结果
     */
    @MemberApiAnnotations.LogoutApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "logout") // 防止重复提交
    @PostMapping("/logout")
    public ResponseEntity<RestResult> logout() {
        return executeWithTryCatch(() -> {
            LoginMember loginMember = BffKit.getLoginMember();
            memberAuthService.logout(loginMember.getMemberId(), loginMember.getToken());
            return RestResult.ok("登出成功");
        }, "登出失败，请稍后重试");
    }

    /**
     * 修改当前登录用户密码
     * @param requestParams 通用请求参数
     * @return 修改结果
     */
    @MemberApiAnnotations.ChangePasswordApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "changePassword")
    @ApiParamValidate(bizParamClass = ChangePasswordParam.class)
    @PostMapping("/changePassword")
    public ResponseEntity<RestResult> changePassword(RequestParams<ChangePasswordParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            ChangePasswordParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 直接传递 requestParams 到 service 层
            memberAuthService.changePassword(requestParams);
            return RestResult.ok("密码修改成功");
        }, "密码修改失败，请稍后重试");
    }

    /**
     * 发送密码重置验证码/邮件
     * @param requestParams 通用请求参数
     * @return 发送结果
     */
    @MemberApiAnnotations.SendPasswordResetCodeApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "sendPasswordResetCode")
    @ApiParamValidate(bizParamClass = SendPasswordResetCodeParam.class)
    @PostMapping("/sendPasswordResetCode")
    public ResponseEntity<RestResult> sendPasswordResetCode(RequestParams<SendPasswordResetCodeParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            SendPasswordResetCodeParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 直接传递 requestParams 到 service 层
            memberAuthService.sendPasswordResetCode(requestParams);
            return RestResult.ok("验证码已发送，请查收");
        }, "发送验证码失败，请稍后重试");
    }

    /**
     * 重置密码
     * @param requestParams 通用请求参数
     * @return 重置结果
     */
    @MemberApiAnnotations.ResetPasswordApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "resetPassword")
    @ApiParamValidate(bizParamClass = ResetPasswordParam.class)
    @PostMapping("/resetPassword")
    public ResponseEntity<RestResult> resetPassword(RequestParams<ResetPasswordParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            ResetPasswordParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 直接传递 requestParams 到 service 层
            memberAuthService.resetPassword(requestParams);
            return RestResult.ok("密码重置成功");
        }, "密码重置失败，请稍后重试");
    }

    /**
     * 更新用户头像
     * @param requestParams 通用请求参数
     * @return 更新结果
     */
    @MemberApiAnnotations.UpdateAvatarApiDoc()
    // TODO：测试完后再开启
    //@PreventDuplicateSubmission(keyPrefix = "updateAvatar")
    @ApiParamValidate(bizParamClass = UpdateAvatarParam.class)
    @PostMapping("/update/avatar")
    public ResponseEntity<RestResult> updateAvatar(RequestParams<UpdateAvatarParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 获取并校验业务参数
            UpdateAvatarParam param = requestParams.getBizParam();
            ParamValidateUtil.validate(param);

            // 直接传递 requestParams 到 service 层
            memberService.updateMemberAvatar(requestParams);
            return RestResult.ok("头像更新成功");
        }, "头像更新失败，请稍后重试");
    }

    /**
     * 获取防重复提交Token
     *
     * @return ResponseEntity<RestResult>
     */
    @MemberApiAnnotations.GetAntiResubmitTokenApiDoc()
    @GetMapping("/token/anti-resubmit")
    public ResponseEntity<RestResult> getAntiResubmitToken(HttpServletRequest request) {
        return executeWithTryCatch(() -> {
            String userId = BffKit.generateMemberIdentifier(request);
            String token = duplicateSubmissionTokenService.generateToken(userId);
            return RestResult.ok("获取成功", token);
        }, "获取防重复提交Token失败，请稍后重试");
    }

}