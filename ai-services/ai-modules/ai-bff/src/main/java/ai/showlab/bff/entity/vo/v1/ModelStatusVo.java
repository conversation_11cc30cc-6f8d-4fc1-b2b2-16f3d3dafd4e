package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 模型状态VO
 * 
 * <AUTHOR>
 */
@Data
public class ModelStatusVo {
    
    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 模型编码
     */
    private String modelCode;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 模型是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 模型服务是否可用
     */
    private Boolean isAvailable;
    
    /**
     * 平均响应时间（毫秒）
     */
    private BigDecimal avgResponseTime;
    
    /**
     * 成功率（百分比）
     */
    private BigDecimal successRate;
    
    /**
     * 每秒查询数
     */
    private BigDecimal qps;
    
    /**
     * 状态检查时间
     */
    private LocalDateTime checkTime;
    
    /**
     * 最后检查时间
     */
    private LocalDateTime lastCheckTime;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 服务版本
     */
    private String serviceVersion;
    
    /**
     * 健康状态描述
     */
    private String healthStatus;
}
