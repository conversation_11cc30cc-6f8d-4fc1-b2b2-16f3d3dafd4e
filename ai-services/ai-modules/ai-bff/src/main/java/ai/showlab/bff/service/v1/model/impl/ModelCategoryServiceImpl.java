package ai.showlab.bff.service.v1.model.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.entity.domain.v1.model.ModelCategory;
import ai.showlab.bff.entity.vo.v1.ModelCategoryVo;
import ai.showlab.bff.mapper.v1.model.ModelCategoryMapper;
import ai.showlab.bff.service.v1.model.IModelCategoryService;
import ai.showlab.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型分类服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Slf4j
@Service
public class ModelCategoryServiceImpl implements IModelCategoryService {

    @Autowired
    private ModelCategoryMapper modelCategoryMapper;

    /**
     * 获取所有可用的模型分类（对外接口）
     * <p>
     * 返回适合前端展示的分类列表，此方法会进行缓存。
     * 排序逻辑已在SQL中处理，无需在Java代码中再次排序。
     * </p>
     *
     * @return 模型分类VO列表
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_CATEGORIES_KEY, key = "'vo_all'")
    public List<ModelCategoryVo> getModelCategories() {
        try {
            // 直接查询VO数据，已在SQL中排序，无需Java转换
            return modelCategoryMapper.selectAllModelCategoriesVo();
        } catch (Exception e) {
            log.error("获取模型分类列表失败", e);
            return List.of();
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MODEL_CATEGORIES_KEY, key = "'all'")
    public List<ModelCategory> getAllCategories() {
        return modelCategoryMapper.selectAllModelCategories();
    }
} 