package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库更新参数
 * <p>
 * 用于更新知识库信息。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class KnowledgeUpdateParam {

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 权限类型 (1-私有, 2-租户共享)
     */
    private Integer permissionType;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
}
