package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

/**
 * 模型输出格式视图对象
 * 用于向前端展示模型支持的输出格式信息
 * 
 * <AUTHOR>
 */
@Data
public class ModelOutputFormatVo {
    
    /**
     * 输出格式ID
     */
    private Long id;
    
    /**
     * 支持的输出类型 (字典: model_output_type), 1-JSON, 2-文本, 3-图片, 4-音频
     */
    private Integer outputType;
    
    /**
     * 该输出格式下是否支持流式返回
     */
    private Boolean supportsStream;
    
    /**
     * 该输出格式下的建议最大 token 数
     */
    private Integer maxTokens;
    
    /**
     * 输出备注说明
     */
    private String description;
}
