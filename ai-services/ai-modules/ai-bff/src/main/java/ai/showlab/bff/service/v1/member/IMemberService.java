package ai.showlab.bff.service.v1.member;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.member.Member;
import ai.showlab.bff.entity.dto.v1.MemberProfileUpdateDto;
import ai.showlab.bff.entity.param.UpdateNicknameParam;
import ai.showlab.bff.entity.param.UpdateAvatarParam;
import ai.showlab.bff.entity.vo.v1.MemberInfoVo;

import java.time.OffsetDateTime;

/**
 * 会员服务接口
 * <p>
 * 定义面向外部用户的会员相关业务逻辑。
 * 方法设计上考虑了性能和安全性。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IMemberService {

    /**
     * 根据会员ID获取会员的公开个人资料
     * <p>
     * 此方法会优先从缓存中获取数据。
     * 缓存失效或未命中时，会从数据库查询并将结果存入缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @return 会员个人资料，如果不存在则返回 null
     */
    Member getMemberProfileById(Long memberId);

    /**
     * 根据会员ID获取会员的公开个人资料 (VO对象)
     * <p>
     * 此方法会优先从缓存中获取数据。
     * 缓存失效或未命中时，会从数据库查询并将结果存入缓存。
     * </p>
     *
     * @param memberId 会员ID
     * @return 会员个人资料VO，如果不存在则返回 null
     */
    MemberInfoVo getMemberInfoVo(Long memberId);

    /**
     * 更新会员的个人资料
     * <p>
     * 此方法会首先更新数据库，然后清除对应的缓存，以保证数据一致性。
     * </p>
     *
     * @param memberId 会员ID
     * @param dto      包含要更新的个人资料的数据传输对象
     */
    void updateMemberProfile(Long memberId, MemberProfileUpdateDto dto);

    /**
     * 更新会员昵称
     * @param requestParams 请求参数，包含新昵称
     */
    void updateMemberNickname(RequestParams<UpdateNicknameParam> requestParams);

    /**
     * 更新会员头像
     * @param requestParams 请求参数，包含新头像URL
     */
    void updateMemberAvatar(RequestParams<UpdateAvatarParam> requestParams);

    /**
     * 根据用户名检查会员是否存在
     *
     * @param username 用户名
     * @return 如果存在返回 true, 否则 false
     */
    boolean isUsernameExists(String username);

    /**
     * 根据邮箱检查会员是否存在
     *
     * @param email 邮箱
     * @return 如果存在返回 true, 否则 false
     */
    boolean isEmailExists(String email);

    /**
     * 根据手机号检查会员是否存在
     *
     * @param phone 手机号
     * @return 如果存在返回 true, 否则 false
     */
    boolean isPhoneExists(String phone);

    /**
     * 创建新会员
     *
     * @param member 待创建的会员对象 (包含用户名、密码等基本信息)
     * @return 新创建会员的ID
     */
    Long createMember(Member member);

    /**
     * 更新会员最后登录信息
     *
     * @param memberId  会员ID
     * @param ip        最后登录IP
     * @param loginTime 最后登录时间
     */
    void updateMemberLastLoginInfo(Long memberId, String ip, OffsetDateTime loginTime);
} 