package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

/**
 * 模型供应商视图对象
 * 用于向前端展示模型供应商信息
 * 
 * <AUTHOR>
 */
@Data
public class ModelProviderVo {
    
    /**
     * 供应商ID
     */
    private Long id;
    
    /**
     * 供应商名称，用于显示 (例如: OpenAI, Google)
     */
    private String providerName;
    
    /**
     * 供应商唯一标识，用于程序内部关联 (例如: openai, google)
     */
    private String providerKey;
    
    /**
     * 渠道类型 (字典: provider_channel_type), 如: 1-official, 2-agent
     */
    private String channelType;

    /**
     * 备注信息
     */
    private String description;
}
