package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 知识库详细视图对象
 * <p>
 * 继承自KnowledgeBaseVo，增加了嵌入模型信息和文档列表。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeBaseDetailVo extends KnowledgeBaseVo {

    /**
     * 嵌入模型ID
     */
    private Long embeddingModelId;

    /**
     * 嵌入模型名称
     */
    private String embeddingModelName;

    /**
     * 文档列表
     */
    private List<KnowledgeDocVo> documents;
}