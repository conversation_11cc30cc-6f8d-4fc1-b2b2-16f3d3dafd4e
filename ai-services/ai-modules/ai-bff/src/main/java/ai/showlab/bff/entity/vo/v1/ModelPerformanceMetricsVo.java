package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 模型性能指标VO
 * 
 * <AUTHOR>
 */
@Data
public class ModelPerformanceMetricsVo {
    
    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 模型编码
     */
    private String modelCode;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 总调用次数
     */
    private Long totalCalls;
    
    /**
     * 成功调用次数
     */
    private Long successCalls;
    
    /**
     * 失败调用次数
     */
    private Long failedCalls;
    
    /**
     * 超时调用次数
     */
    private Long timeoutCalls;
    
    /**
     * 成功率（百分比）
     */
    private BigDecimal successRate;
    
    /**
     * 平均响应时间（毫秒）
     */
    private BigDecimal avgResponseTime;
    
    /**
     * 最小响应时间（毫秒）
     */
    private BigDecimal minResponseTime;
    
    /**
     * 最大响应时间（毫秒）
     */
    private BigDecimal maxResponseTime;
    
    /**
     * 独立用户数
     */
    private Long uniqueUsers;
    
    /**
     * 总输入Token数
     */
    private Long totalInputTokens;
    
    /**
     * 总输出Token数
     */
    private Long totalOutputTokens;
    
    /**
     * 平均每小时查询数
     */
    private BigDecimal avgQph;
    
    /**
     * 指标统计时间
     */
    private LocalDateTime metricsTime;
}
