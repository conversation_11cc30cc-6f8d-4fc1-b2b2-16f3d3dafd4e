package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

/**
 * 模型列表视图对象
 * 用于向前端展示模型列表信息，包含基本信息但不包含详细的特性和输出格式
 * 
 * <AUTHOR>
 */
@Data
public class ModelListVo {
    
    /**
     * 模型ID
     */
    private Long id;
    
    /**
     * 模型名称
     */
    private String name;
    
    /**
     * 模型唯一编码（如 gpt-4-turbo）
     */
    private String code;
    
    /**
     * 模型来源 (字典: model_source), 1-第三方, 2-内部模型
     */
    private Integer source;
    
    /**
     * 模型版本，如 "2024-05"
     */
    private String version;
    
    /**
     * 是否支持流式响应
     */
    private Boolean supportsStream;
    
    /**
     * 是否支持函数调用
     */
    private Boolean supportsFunction;

    /**
     * 模型详细描述
     */
    private String description;

    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 供应商名称
     */
    private String providerName;
}
