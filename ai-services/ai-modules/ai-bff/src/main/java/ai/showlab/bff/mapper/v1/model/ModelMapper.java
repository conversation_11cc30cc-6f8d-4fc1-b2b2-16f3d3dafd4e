package ai.showlab.bff.mapper.v1.model;

import ai.showlab.bff.entity.domain.v1.model.Model;
import ai.showlab.bff.entity.param.ModelListParam;
import ai.showlab.bff.entity.vo.v1.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型 数据层
 * <p>
 * 提供模型信息的基础数据库操作。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Repository
public interface ModelMapper {

    /**
     * 根据ID查询模型
     *
     * @param id 模型ID
     * @return 模型对象
     */
    Model selectModelById(Long id);

    /**
     * 根据编码查询模型
     *
     * @param code 模型编码
     * @return 模型对象
     */
    Model selectModelByCode(String code);

    /**
     * 根据分类ID查询模型
     *
     * @param categoryId 分类ID
     * @return 模型列表
     */
    List<Model> selectModelsByCategoryId(Long categoryId);

    /**
     * 根据供应商ID查询模型
     *
     * @param providerId 供应商ID
     * @return 模型列表
     */
    List<Model> selectModelsByProviderId(Long providerId);

    /**
     * 查询所有可用的模型
     *
     * @return 模型列表
     */
    List<Model> selectAllModels();

    /**
     * 根据条件分页查询模型列表
     *
     * @param param 查询参数
     * @return 模型列表
     */
    List<Model> selectModelListByCondition(@Param("param") ModelListParam param);

    /**
     * 根据条件分页查询模型列表VO（直接返回VO，包含关联信息）
     *
     * @param param 查询参数
     * @return 模型列表VO
     */
    List<ModelListVo> selectModelListVoByCondition(@Param("param") ModelListParam param);

    /**
     * 根据条件统计模型总数
     *
     * @param param 查询参数
     * @return 总数
     */
    long countModelsByCondition(@Param("param") ModelListParam param);

    /**
     * 查询热门模型列表
     * 根据调用次数、评分等指标排序
     *
     * @param limit 限制数量
     * @return 热门模型列表
     */
    List<Model> selectPopularModels(@Param("limit") int limit);

    /**
     * 查询热门模型列表VO（直接返回VO，包含关联信息）
     *
     * @param limit 限制数量
     * @return 热门模型列表VO
     */
    List<ModelListVo> selectPopularModelsVo(@Param("limit") int limit);

    /**
     * 根据模型ID查询完整的模型信息（包含关联数据）
     *
     * @param id 模型ID
     * @return 模型对象（包含关联信息）
     */
    Model selectModelWithDetailsById(Long id);

    /**
     * 根据模型编码查询完整的模型信息（包含关联数据）
     *
     * @param code 模型编码
     * @return 模型对象（包含关联信息）
     */
    Model selectModelWithDetailsByCode(String code);

    /**
     * 根据ID查询模型详情VO基础信息（包含分类和供应商，不包含特性和输出格式）
     *
     * @param id 模型ID
     * @return 模型详情VO基础信息
     */
    ModelVo selectModelVoBaseById(Long id);

    /**
     * 根据编码查询模型详情VO基础信息（包含分类和供应商，不包含特性和输出格式）
     *
     * @param code 模型编码
     * @return 模型详情VO基础信息
     */
    ModelVo selectModelVoBaseByCode(String code);

    // ==================== 会员权限相关方法 ====================

    /**
     * 根据条件统计会员可访问的模型总数
     *
     * @param param    查询参数
     * @param memberId 会员ID
     * @return 总数
     */
    long countAccessibleModelsByCondition(@Param("param") ModelListParam param, @Param("memberId") Long memberId);

    /**
     * 根据条件分页查询会员可访问的模型列表VO
     *
     * @param param    查询参数
     * @param memberId 会员ID
     * @return 可访问的模型列表VO
     */
    List<ModelListVo> selectAccessibleModelListVoByCondition(@Param("param") ModelListParam param, @Param("memberId") Long memberId);

    /**
     * 检查会员是否有权限访问指定模型
     *
     * @param memberId 会员ID
     * @param modelId  模型ID
     * @return 是否有访问权限
     */
    boolean checkMemberModelAccess(@Param("memberId") Long memberId, @Param("modelId") Long modelId);

    // ==================== 会员个性化功能方法 ====================

    /**
     * 查询会员收藏的模型列表
     *
     * @param memberId 会员ID
     * @return 收藏的模型列表VO
     */
    List<ModelListVo> selectFavoriteModelsByMemberId(@Param("memberId") Long memberId);

    /**
     * 检查会员是否收藏了指定模型
     *
     * @param memberId 会员ID
     * @param modelId  模型ID
     * @return 是否已收藏
     */
    boolean isMemberFavoriteModel(@Param("memberId") Long memberId, @Param("modelId") Long modelId);

    /**
     * 添加会员收藏模型
     *
     * @param memberId 会员ID
     * @param modelId  模型ID
     */
    void addMemberFavoriteModel(@Param("memberId") Long memberId, @Param("modelId") Long modelId);

    /**
     * 移除会员收藏模型
     *
     * @param memberId 会员ID
     * @param modelId  模型ID
     */
    void removeMemberFavoriteModel(@Param("memberId") Long memberId, @Param("modelId") Long modelId);

    /**
     * 查询会员最近使用的模型列表
     *
     * @param memberId 会员ID
     * @param limit    限制数量
     * @return 最近使用的模型列表VO
     */
    List<ModelListVo> selectRecentModelsByMemberId(@Param("memberId") Long memberId, @Param("limit") int limit);

    /**
     * 查询为会员推荐的模型列表
     *
     * @param memberId 会员ID
     * @param limit    限制数量
     * @return 推荐的模型列表VO
     */
    List<ModelListVo> selectRecommendedModelsByMemberId(@Param("memberId") Long memberId, @Param("limit") int limit);

    // ==================== 统计和监控方法 ====================

    /**
     * 查询模型性能指标
     *
     * @param modelId 模型ID
     * @return 性能指标VO
     */
    ModelPerformanceMetricsVo selectModelPerformanceMetrics(@Param("modelId") Long modelId);

    /**
     * 查询会员使用统计
     *
     * @param memberId 会员ID
     * @return 使用统计VO
     */
    MemberUsageStatsVo selectMemberUsageStats(@Param("memberId") Long memberId);

    /**
     * 查询会员对特定模型的使用统计
     *
     * @param memberId 会员ID
     * @param modelId  模型ID
     * @return 使用统计VO
     */
    MemberModelUsageStatsVo selectMemberModelUsageStats(@Param("memberId") Long memberId, @Param("modelId") Long modelId);
}