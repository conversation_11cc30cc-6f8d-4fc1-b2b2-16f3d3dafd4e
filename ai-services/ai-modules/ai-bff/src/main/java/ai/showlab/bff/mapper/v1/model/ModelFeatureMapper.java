package ai.showlab.bff.mapper.v1.model;

import ai.showlab.bff.entity.domain.v1.model.ModelFeature;
import ai.showlab.bff.entity.vo.v1.ModelFeatureVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型特性 数据层
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Repository
public interface ModelFeatureMapper {

    /**
     * 根据模型ID查询其所有特性
     *
     * @param modelId 模型ID
     * @return 模型特性列表
     */
    List<ModelFeature> selectModelFeaturesByModelId(Long modelId);

    /**
     * 根据模型ID查询其所有特性VO
     *
     * @param modelId 模型ID
     * @return 模型特性VO列表
     */
    List<ModelFeatureVo> selectModelFeatureVosByModelId(Long modelId);
} 