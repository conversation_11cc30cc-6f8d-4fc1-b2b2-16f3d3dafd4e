package ai.showlab.bff.service.v1.knowledge;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.KnowledgeBaseDetailVo;
import ai.showlab.bff.entity.vo.v1.KnowledgeBaseVo;
import ai.showlab.bff.entity.vo.v1.KnowledgeDocVo;

import java.util.List;

/**
 * 知识库服务接口
 * <p>
 * 定义了面向外部用户的知识库相关业务逻辑。
 * 包括知识库的CRUD操作、文档管理等功能。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public interface IKnowledgeService {

    /**
     * 获取指定会员的知识库列表
     * <p>
     * 支持按关键词搜索和权限类型过滤。
     * 此方法会进行缓存以提高性能。
     * </p>
     *
     * @param requestParams 请求参数，包含搜索条件
     * @return 知识库简要信息列表
     */
    List<KnowledgeBaseVo> getMemberKnowledgeBases(RequestParams<KnowledgeListParam> requestParams);

    /**
     * 获取知识库详情
     * <p>
     * 包含知识库基本信息、嵌入模型信息和文档列表。
     * 会校验用户权限，只能查看自己的知识库。
     * </p>
     *
     * @param requestParams 请求参数，包含知识库ID
     * @return 知识库详细信息
     */
    KnowledgeBaseDetailVo getKnowledgeBaseDetail(RequestParams<KnowledgeDetailParam> requestParams);

    /**
     * 创建知识库
     * <p>
     * 创建新的知识库，会验证嵌入模型是否存在。
     * 创建成功后会清除相关缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含知识库创建信息
     * @return 创建后的知识库信息
     */
    KnowledgeBaseVo createKnowledgeBase(RequestParams<KnowledgeCreateParam> requestParams);

    /**
     * 更新知识库
     * <p>
     * 更新知识库基本信息，会校验用户权限。
     * 更新成功后会清除相关缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含知识库更新信息
     */
    void updateKnowledgeBase(RequestParams<KnowledgeUpdateParam> requestParams);

    /**
     * 删除知识库
     * <p>
     * 软删除知识库及其所有文档和文本块。
     * 会校验用户权限，只能删除自己的知识库。
     * 删除成功后会清除相关缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含知识库ID
     */
    void deleteKnowledgeBase(RequestParams<KnowledgeDeleteParam> requestParams);

    /**
     * 上传文档到知识库
     * <p>
     * 将文档信息记录到数据库，并触发后续的文档处理流程。
     * 会校验用户对知识库的权限。
     * </p>
     *
     * @param requestParams 请求参数，包含文档上传信息
     * @return 上传后的文档信息
     */
    KnowledgeDocVo uploadDocument(RequestParams<KnowledgeDocUploadParam> requestParams);

    /**
     * 删除知识库中的文档
     * <p>
     * 软删除文档及其所有文本块。
     * 会校验用户权限，只能删除自己知识库中的文档。
     * </p>
     *
     * @param requestParams 请求参数，包含文档删除信息
     */
    void deleteDocument(RequestParams<KnowledgeDocDeleteParam> requestParams);
}