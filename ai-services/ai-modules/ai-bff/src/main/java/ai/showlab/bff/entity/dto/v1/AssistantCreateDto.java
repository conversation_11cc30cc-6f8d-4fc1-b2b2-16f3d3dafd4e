package ai.showlab.bff.entity.dto.v1;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * AI助手创建 DTO
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class AssistantCreateDto {

    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    @NotBlank(message = "名称不能为空")
    private String name;

    private String description;

    private String iconUrl;

    @NotBlank(message = "提示模板不能为空")
    private String promptTemplate;

    private Integer interactionMode;

    private String modelSuggestions;

    private Boolean isPublic;

    private Integer sortOrder;
} 