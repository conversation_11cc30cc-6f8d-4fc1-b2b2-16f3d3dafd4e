package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.entity.domain.v1.member.Member;
import ai.showlab.bff.entity.dto.v1.MemberProfileUpdateDto;
import ai.showlab.bff.entity.param.UpdateAvatarParam;
import ai.showlab.bff.entity.param.UpdateNicknameParam;
import ai.showlab.bff.entity.vo.v1.MemberInfoVo;
import ai.showlab.bff.mapper.v1.member.MemberMapper;
import ai.showlab.bff.service.v1.member.IMemberService;
import ai.showlab.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;

/**
 * 会员服务实现类
 * <p>
 * 实现了会员相关的业务逻辑，包括缓存处理和异常管理。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class MemberServiceImpl implements IMemberService {

    @Autowired
    private MemberMapper memberMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_PROFILE, key = "#memberId")
    public Member getMemberProfileById(Long memberId) {
        // XML中 selectMemberById 只查询安全字段，可直接返回
        return memberMapper.selectMemberById(memberId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_INFO_VO, key = "#memberId") // 为VO对象添加单独缓存
    public MemberInfoVo getMemberInfoVo(Long memberId) {
        Member member = getMemberProfileById(memberId);
        if (member == null) {
            return null;
        }
        MemberInfoVo memberInfoVo = new MemberInfoVo();
        BeanUtils.copyProperties(member, memberInfoVo);
        return memberInfoVo;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void updateMemberProfile(Long memberId, MemberProfileUpdateDto dto) {
        // 1. 校验用户是否存在
        Member existingMember = memberMapper.selectMemberById(memberId);
        if (existingMember == null) {
            throw new ServiceException("用户不存在");
        }

        // 2. 将 DTO 转换为 Domain 对象
        Member memberToUpdate = new Member();
        BeanUtils.copyProperties(dto, memberToUpdate);
        // 确保ID被设置
        memberToUpdate.setId(memberId);

        // 3. 执行更新操作
        int result = memberMapper.updateMemberProfile(memberToUpdate);
        if (result == 0) {
            // 通常意味着记录在并发场景下被删除，或者更新条件不匹配
            throw new ServiceException("更新个人资料失败，请稍后重试");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    @CustomCache(value = CacheConstants.BFF_MEMBER_INFO_VO, key = "#currentMemberId")
    public void updateMemberNickname(RequestParams<UpdateNicknameParam> requestParams) {
        Long currentMemberId = BffKit.getCurrentMemberId();
        // 获取业务参数
        UpdateNicknameParam param = requestParams.getBizParam();

        Member existingMember = memberMapper.selectMemberById(currentMemberId);
        if (existingMember == null) {
            throw new BusinessException("会员不存在");
        }
        Member memberToUpdate = new Member();
        memberToUpdate.setId(currentMemberId);
        memberToUpdate.setNickname(param.getNickname());
        int result = memberMapper.updateMemberProfile(memberToUpdate);
        if (result == 0) {
            throw new BusinessException("更新昵称失败，请稍后重试");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    @CustomCache(value = CacheConstants.BFF_MEMBER_INFO_VO, key = "#currentMemberId")
    public void updateMemberAvatar(RequestParams<UpdateAvatarParam> requestParams) {
        Long currentMemberId = BffKit.getCurrentMemberId();
        // 获取业务参数
        UpdateAvatarParam param = requestParams.getBizParam();

        Member existingMember = memberMapper.selectMemberById(currentMemberId);
        if (existingMember == null) {
            throw new BusinessException("会员不存在");
        }
        Member memberToUpdate = new Member();
        memberToUpdate.setId(currentMemberId);
        memberToUpdate.setAvatar(param.getAvatarUrl());
        int result = memberMapper.updateMemberProfile(memberToUpdate);
        if (result == 0) {
            throw new BusinessException("更新头像失败，请稍后重试");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isUsernameExists(String username) {
        return memberMapper.selectMemberByUsername(username) != null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isEmailExists(String email) {
        return memberMapper.selectMemberByEmail(email) != null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isPhoneExists(String phone) {
        return memberMapper.selectMemberByPhone(phone) != null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void updateMemberLastLoginInfo(Long memberId, String ip, OffsetDateTime loginTime) {
        Member member = new Member();
        member.setId(memberId);
        member.setLastLoginIp(ip);
        member.setLastLoginTime(loginTime);
        memberMapper.updateMemberLastLogin(member);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public Long createMember(Member member) {
        if (memberMapper.insertMember(member) > 0) {
            return member.getId();
        } else {
            throw new BusinessException("创建会员失败");
        }
    }
} 