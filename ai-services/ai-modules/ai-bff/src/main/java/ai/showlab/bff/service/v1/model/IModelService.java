package ai.showlab.bff.service.v1.model;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.model.Model;
import ai.showlab.bff.entity.param.ModelByCodeParam;
import ai.showlab.bff.entity.param.ModelDetailParam;
import ai.showlab.bff.entity.param.ModelListParam;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.common.core.web.page.PageResult;

import java.util.List;

/**
 * 模型服务接口
 * <p>
 * 定义了获取和管理模型的业务逻辑，面向外部普通会员。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IModelService {

    /**
     * 分页查询模型列表
     * <p>
     * 支持按分类、供应商、特性等条件筛选，返回适合前端展示的VO对象。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 分页结果
     */
    PageResult<ModelListVo> getModelList(RequestParams<ModelListParam> requestParams);

    /**
     * 根据ID获取模型详情
     * <p>
     * 返回包含完整信息的模型详情，包括特性、输出格式等。
     * 此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 模型详情VO
     */
    ModelVo getModelDetail(RequestParams<ModelDetailParam> requestParams);

    /**
     * 根据编码获取模型信息
     * <p>
     * 用于模型调用前的信息获取，此方法会进行缓存。
     * </p>
     *
     * @param requestParams 请求参数，包含模型编码
     * @return 模型详情VO
     */
    ModelVo getModelByCode(RequestParams<ModelByCodeParam> requestParams);

    /**
     * 获取热门模型列表
     * <p>
     * 返回热门推荐的模型列表，用于首页展示。
     * 此方法会进行缓存。
     * </p>
     *
     * @return 热门模型列表
     */
    List<ModelListVo> getPopularModels();

    // ==================== 会员权限相关方法 ====================

    /**
     * 获取当前会员可访问的模型列表
     * <p>
     * 根据会员等级、角色、地区等权限控制返回可访问的模型。
     * </p>
     *
     * @param requestParams 请求参数，包含筛选条件和分页信息
     * @return 可访问的模型分页结果
     */
    PageResult<ModelListVo> getAccessibleModels(RequestParams<ModelListParam> requestParams);

    /**
     * 检查当前会员是否有权限访问指定模型
     * <p>
     * 根据模型可见性规则验证会员权限。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 是否有访问权限
     */
    boolean checkModelAccess(RequestParams<ModelDetailParam> requestParams);

    // ==================== 会员个性化功能 ====================

    /**
     * 获取当前会员收藏的模型列表
     * <p>
     * 返回会员收藏的模型，按收藏时间倒序排列。
     * </p>
     *
     * @return 收藏的模型列表
     */
    List<ModelListVo> getFavoriteModels();

    /**
     * 切换模型收藏状态
     * <p>
     * 如果已收藏则取消收藏，如果未收藏则添加收藏。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 操作后的收藏状态（true-已收藏，false-未收藏）
     */
    boolean toggleFavoriteModel(RequestParams<ModelDetailParam> requestParams);

    /**
     * 获取当前会员最近使用的模型
     * <p>
     * 根据调用日志返回最近使用的模型，按使用时间倒序排列。
     * </p>
     *
     * @return 最近使用的模型列表
     */
    List<ModelListVo> getRecentModels();

    /**
     * 获取为当前会员推荐的模型
     * <p>
     * 基于会员使用习惯、等级、热门度等因素推荐模型。
     * </p>
     *
     * @return 推荐的模型列表
     */
    List<ModelListVo> getRecommendedModels();

    // ==================== 模型状态和监控 ====================

    /**
     * 检查模型服务状态
     * <p>
     * 检查指定模型的服务可用性和状态信息。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 模型状态VO（包含可用性、响应时间等）
     */
    ModelStatusVo checkModelStatus(RequestParams<ModelDetailParam> requestParams);

    /**
     * 获取模型性能指标
     * <p>
     * 获取指定模型的性能指标，如平均响应时间、成功率等。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 性能指标VO
     */
    ModelPerformanceMetricsVo getModelMetrics(RequestParams<ModelDetailParam> requestParams);

    // ==================== 使用统计功能 ====================

    /**
     * 获取当前会员的模型使用统计
     * <p>
     * 返回会员的总体使用统计，包括调用次数、使用时长等。
     * </p>
     *
     * @return 使用统计VO
     */
    MemberUsageStatsVo getMemberUsageStats();

    /**
     * 获取当前会员对指定模型的使用统计
     * <p>
     * 返回会员对特定模型的使用统计数据。
     * </p>
     *
     * @param requestParams 请求参数，包含模型ID
     * @return 模型使用统计VO
     */
    MemberModelUsageStatsVo getModelUsageStats(RequestParams<ModelDetailParam> requestParams);

    /**
     * 获取所有可用的模型（内部方法）
     * <p>
     * 此方法会进行缓存，主要用于内部业务逻辑。
     * </p>
     *
     * @return 模型列表
     */
    List<Model> getAllModels();

    /**
     * 根据ID获取模型信息（内部方法）
     * <p>
     * 此方法会进行缓存，主要用于内部业务逻辑。
     * </p>
     *
     * @param id 模型ID
     * @return 模型对象
     */
    Model getModelById(Long id);

    /**
     * 根据编码获取模型信息（内部方法）
     * <p>
     * 此方法会进行缓存，主要用于内部业务逻辑。
     * </p>
     *
     * @param code 模型编码
     * @return 模型对象
     */
    Model getModelByCode(String code);

    /**
     * 根据分类ID获取模型列表（内部方法）
     * <p>
     * 此方法可以利用 {@link #getAllModels()} 的缓存结果进行过滤，以减少数据库访问。
     * </p>
     *
     * @param categoryId 分类ID
     * @return 模型列表
     */
    List<Model> getModelsByCategoryId(Long categoryId);

    /**
     * 根据供应商ID获取模型列表（内部方法）
     * <p>
     * 此方法可以利用 {@link #getAllModels()} 的缓存结果进行过滤。
     * </p>
     *
     * @param providerId 供应商ID
     * @return 模型列表
     */
    List<Model> getModelsByProviderId(Long providerId);
}