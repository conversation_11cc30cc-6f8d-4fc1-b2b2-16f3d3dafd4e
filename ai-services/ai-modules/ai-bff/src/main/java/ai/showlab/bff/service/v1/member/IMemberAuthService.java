package ai.showlab.bff.service.v1.member;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.member.MemberAuth;
import ai.showlab.bff.entity.param.MemberLoginParam;
import ai.showlab.bff.entity.param.MemberRegisterParam;
import ai.showlab.bff.entity.param.ChangePasswordParam;
import ai.showlab.bff.entity.param.SendPasswordResetCodeParam;
import ai.showlab.bff.entity.param.ResetPasswordParam;

import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员认证服务接口
 * <p>
 * 定义了处理会员认证方式（如密码、社交账户）的业务逻辑。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IMemberAuthService {

    /**
     * 为指定会员注册一种新的认证方式
     *
     * @param memberId   会员ID
     * @param authType   认证类型 (如: "password", "wechat")
     * @param identifier 唯一标识符 (如: username, openid)
     * @param credential 凭证 (如: 加密后的密码)
     */
    void registerNewAuth(Long memberId, Integer authType, String identifier, String credential);

    /**
     * 用户注册
     * <p>
     * 负责创建新会员并为其设置密码认证方式。
     * </p>
     *
     * @param requestParams 请求参数
     */
    @Transactional
    void register(RequestParams<MemberRegisterParam> requestParams);

    /**
     * 用户登录
     * <p>
     * 负责验证用户身份并生成会话令牌。
     * 同时创建会话记录，支持JWT + Session混合方案。
     * RequestParams中已包含HttpServletRequest，可通过便捷方法获取IP等信息。
     * </p>
     *
     * @param requestParams 请求参数，包含用户标识、密码和HttpServletRequest
     * @return 会话令牌 (Token)
     */
    String login(RequestParams<MemberLoginParam> requestParams);

    /**
     * 用户登出
     * <p>
     * 负责使当前用户的会话令牌失效。
     * </p>
     *
     * @param memberId 会员ID
     * @param token    会话令牌
     */
    void logout(Long memberId, String token);

    /**
     * 获取指定会员绑定的所有认证方式 (不含凭证)
     * <p>
     * 此方法会进行缓存，以提高查询效率。
     * </p>
     *
     * @param memberId 会员ID
     * @return 认证方式列表
     */
    List<MemberAuth> getMemberAuths(Long memberId);

    /**
     * 验证用户的凭证是否正确
     *
     * @param authType      认证类型
     * @param identifier    唯一标识符
     * @param rawCredential 未经加密的原始凭证
     * @return 验证成功返回对应的 MemberAuth 对象 (包含 memberId)，失败则抛出 ServiceException
     */
    MemberAuth verifyCredential(Integer authType, String identifier, String rawCredential);

    /**
     * 修改指定认证方式的凭证 (例如，修改密码)
     *
     * @param memberId      会员ID
     * @param authType      认证类型
     * @param oldCredential 旧的原始凭证，用于验证身份，如果为null则不校验
     * @param newCredential 新的原始凭证
     */
    void changeCredential(Long memberId, Integer authType, String oldCredential, String newCredential);

    /**
     * 修改当前登录用户密码
     * <p>
     * 验证旧密码并更新为新密码。
     * </p>
     *
     * @param requestParams 请求参数，包含旧密码和新密码
     */
    void changePassword(RequestParams<ChangePasswordParam> requestParams);

    /**
     * 发送密码重置验证码/邮件
     * <p>
     * 根据用户标识（用户名/邮箱/手机号）发送用于密码重置的验证码。
     * </p>
     *
     * @param requestParams 请求参数，包含用户标识
     */
    void sendPasswordResetCode(RequestParams<SendPasswordResetCodeParam> requestParams);

    /**
     * 重置密码
     * <p>
     * 使用验证码和新密码重置用户密码。
     * </p>
     *
     * @param requestParams 请求参数，包含用户标识、验证码和新密码
     */
    void resetPassword(RequestParams<ResetPasswordParam> requestParams);

    /**
     * 解绑一个认证方式
     *
     * @param memberId 会员ID
     * @param authType 要解绑的认证类型
     */
    void unbindAuth(Long memberId, Integer authType);
} 