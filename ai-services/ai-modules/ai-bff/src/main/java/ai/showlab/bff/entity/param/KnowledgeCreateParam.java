package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库创建参数
 * <p>
 * 用于创建新的知识库。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
public class KnowledgeCreateParam {

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 嵌入模型ID
     */
    @NotNull(message = "必须指定一个嵌入模型")
    private Long embeddingModelId;

    /**
     * 权限类型 (1-私有, 2-租户共享)
     * 默认为私有
     */
    private Integer permissionType = 1;
    
    /**
     * 排序值
     */
    private Integer sortOrder = 0;
}
