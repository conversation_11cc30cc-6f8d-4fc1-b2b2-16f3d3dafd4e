package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.util.List;

/**
 * AI模型视图对象
 * 用于向前端展示模型信息，包含关联的分类和供应商信息
 * 
 * <AUTHOR>
 */
@Data
public class ModelVo {
    
    /**
     * 模型ID
     */
    private Long id;
    
    /**
     * 模型名称
     */
    private String name;
    
    /**
     * 模型唯一编码（如 gpt-4-turbo）
     */
    private String code;
    
    /**
     * 模型来源 (字典: model_source), 1-第三方, 2-内部模型
     */
    private Integer source;
    
    /**
     * 模型版本，如 "2024-05"
     */
    private String version;
    
    /**
     * 是否支持流式响应
     */
    private Boolean supportsStream;
    
    /**
     * 是否支持函数调用
     */
    private Boolean supportsFunction;

    /**
     * 模型详细描述
     */
    private String description;

    /**
     * 模型分类信息
     */
    private ModelCategoryVo category;
    
    /**
     * 模型供应商信息
     */
    private ModelProviderVo provider;
    
    /**
     * 模型特性列表
     */
    private List<ModelFeatureVo> features;
    
    /**
     * 支持的输出格式列表
     */
    private List<ModelOutputFormatVo> outputFormats;
}
