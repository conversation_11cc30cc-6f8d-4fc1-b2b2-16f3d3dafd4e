package ai.showlab.bff.common.docs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 知识库相关API文档注解
 * <p>
 * 为知识库相关的接口提供统一的API文档注解。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
public class KnowledgeApiAnnotations {

    /**
     * 获取知识库列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取知识库列表",
            description = "获取当前会员的知识库列表，支持按关键词搜索和权限类型过滤"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetKnowledgeListApiDoc {
    }

    /**
     * 获取知识库详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取知识库详情",
            description = "获取指定知识库的详细信息，包括基本信息、嵌入模型信息和文档列表"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "知识库不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetKnowledgeDetailApiDoc {
    }

    /**
     * 创建知识库API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "创建知识库",
            description = "创建新的知识库，需要指定名称、描述、嵌入模型等信息"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface CreateKnowledgeApiDoc {
    }

    /**
     * 更新知识库API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "更新知识库",
            description = "更新知识库的基本信息，如名称、描述、权限类型等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "知识库不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UpdateKnowledgeApiDoc {
    }

    /**
     * 删除知识库API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "删除知识库",
            description = "软删除指定的知识库及其所有文档和文本块"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "知识库不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface DeleteKnowledgeApiDoc {
    }

    /**
     * 上传文档API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "上传文档到知识库",
            description = "将文档上传到指定的知识库中，系统会自动进行文档处理和向量化"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上传成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "知识库不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UploadDocumentApiDoc {
    }

    /**
     * 删除文档API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "删除知识库中的文档",
            description = "从知识库中删除指定的文档及其所有文本块"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "文档不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface DeleteDocumentApiDoc {
    }
}
