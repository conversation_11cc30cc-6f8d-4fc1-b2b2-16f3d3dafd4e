package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 模型列表查询参数
 * 
 * <AUTHOR>
 */
@Data
public class ModelListParam {
    
    /**
     * 分类ID，可选
     */
    private Long categoryId;
    
    /**
     * 供应商ID，可选
     */
    private Long providerId;
    
    /**
     * 模型来源，可选 (字典: model_source), 1-第三方, 2-内部模型
     */
    private Integer source;
    
    /**
     * 是否支持流式响应，可选
     */
    private Boolean supportsStream;
    
    /**
     * 是否支持函数调用，可选
     */
    private Boolean supportsFunction;
    
    /**
     * 关键词搜索，可选（搜索模型名称和描述）
     */
    private String keyword;
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
