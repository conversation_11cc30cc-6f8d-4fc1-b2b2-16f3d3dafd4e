package ai.showlab.bff.common.docs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 模型相关API文档注解
 * 
 * <AUTHOR>
 */
public class ModelApiAnnotations {

    /**
     * 获取模型分类列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取模型分类列表", description = "获取所有可用的AI模型分类，用于前端分类筛选")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetModelCategoriesApiDoc {
    }

    /**
     * 获取模型列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取模型列表", description = "根据条件分页查询AI模型列表，支持按分类、供应商、特性等筛选")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetModelListApiDoc {
    }

    /**
     * 获取模型详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取模型详情", description = "根据模型ID获取详细信息，包括特性、输出格式等")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetModelDetailApiDoc {
    }

    /**
     * 根据编码获取模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "根据编码获取模型", description = "根据模型编码获取模型信息，用于模型调用前的信息获取")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetModelByCodeApiDoc {
    }

    /**
     * 获取热门模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取热门模型", description = "获取热门推荐的AI模型列表，用于首页展示")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetPopularModelsApiDoc {
    }

    // ==================== 会员权限相关API ====================

    /**
     * 获取可访问模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取可访问模型", description = "根据会员等级、角色、地区等权限获取当前会员可访问的模型列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAccessibleModelsApiDoc {
    }

    /**
     * 检查模型访问权限API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "检查模型访问权限", description = "验证当前会员是否有权限使用指定模型")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface CheckModelAccessApiDoc {
    }

    // ==================== 会员个性化功能API ====================

    /**
     * 获取收藏模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取收藏模型", description = "获取当前会员收藏的模型列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetFavoriteModelsApiDoc {
    }

    /**
     * 切换收藏状态API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "收藏/取消收藏模型", description = "切换指定模型的收藏状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "操作成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "404", description = "模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface ToggleFavoriteModelApiDoc {
    }

    /**
     * 获取最近使用模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取最近使用模型", description = "获取当前会员最近使用的模型列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetRecentModelsApiDoc {
    }

    /**
     * 获取推荐模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取推荐模型", description = "基于会员使用习惯、等级等为当前会员推荐模型")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetRecommendedModelsApiDoc {
    }

    // ==================== 模型状态和监控API ====================

    /**
     * 检查模型状态API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "检查模型状态", description = "检查指定模型的服务可用性和状态信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检查成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface CheckModelStatusApiDoc {
    }

    /**
     * 获取模型性能指标API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取模型性能指标", description = "获取指定模型的性能指标，如响应时间、成功率等")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetModelMetricsApiDoc {
    }

    // ==================== 使用统计API ====================

    /**
     * 获取会员使用统计API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取会员使用统计", description = "获取当前会员的模型使用统计数据")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetMemberUsageStatsApiDoc {
    }

    /**
     * 获取模型使用统计API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(summary = "获取模型使用统计", description = "获取当前会员对指定模型的使用统计")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未登录"),
            @ApiResponse(responseCode = "404", description = "模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetModelUsageStatsApiDoc {
    }
}
