package ai.showlab.bff.service.v1.model;

import ai.showlab.bff.entity.domain.v1.model.ModelCategory;
import ai.showlab.bff.entity.vo.v1.ModelCategoryVo;

import java.util.List;

/**
 * 模型分类服务接口
 * <p>
 * 定义了获取模型分类的业务逻辑，面向外部普通会员。
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
public interface IModelCategoryService {

    /**
     * 获取所有可用的模型分类（对外接口）
     * <p>
     * 返回适合前端展示的分类列表，此方法会进行缓存。
     * </p>
     *
     * @return 模型分类VO列表
     */
    List<ModelCategoryVo> getModelCategories();

    /**
     * 获取所有可用的模型分类（内部方法）
     * <p>
     * 此方法会进行缓存，因为分类信息是基础数据，不常变动。
     * </p>
     *
     * @return 模型分类列表
     */
    List<ModelCategory> getAllCategories();
}