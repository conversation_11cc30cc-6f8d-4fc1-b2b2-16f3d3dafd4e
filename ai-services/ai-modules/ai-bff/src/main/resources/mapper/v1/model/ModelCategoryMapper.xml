<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelCategoryMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.ModelCategory" id="ModelCategoryResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- ModelCategoryVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.ModelCategoryVo" id="ModelCategoryVoResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
    </resultMap>

    <sql id="selectModelCategoryVo">
        select id, code, name, delete_time, create_time, update_time from a_model_category
    </sql>

    <select id="selectModelCategoryById" parameterType="Long" resultMap="ModelCategoryResult">
        <include refid="selectModelCategoryVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectModelCategoryByCode" parameterType="String" resultMap="ModelCategoryResult">
        <include refid="selectModelCategoryVo"/>
        where code = #{code} and delete_time is null
    </select>

    <select id="selectAllModelCategories" resultMap="ModelCategoryResult">
        <include refid="selectModelCategoryVo"/>
        where delete_time is null
        order by
        case when sort_order is null then 1 else 0 end,
        sort_order asc,
        create_time asc
    </select>

    <!-- 查询所有模型分类VO（直接返回VO） -->
    <select id="selectAllModelCategoriesVo" resultMap="ModelCategoryVoResult">
        select id, code, name
        from a_model_category
        where delete_time is null
        order by
        case when sort_order is null then 1 else 0 end,
        sort_order asc,
        create_time asc
    </select>

</mapper>