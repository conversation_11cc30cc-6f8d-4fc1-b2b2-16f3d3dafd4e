<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelFeatureMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.ModelFeature" id="ModelFeatureResult">
        <result property="id" column="id"/>
        <result property="modelId" column="model_id"/>
        <result property="key" column="key"/>
        <result property="value" column="value"/>
        <result property="description" column="description"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- ModelFeatureVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.ModelFeatureVo" id="ModelFeatureVoResult">
        <result property="id" column="id"/>
        <result property="key" column="key"/>
        <result property="value" column="value"/>
        <result property="description" column="description"/>
    </resultMap>

    <sql id="selectModelFeatureVo">
        select id, model_id, "key", "value", description, delete_time, create_time, update_time
        from a_model_feature
    </sql>

    <select id="selectModelFeaturesByModelId" parameterType="Long" resultMap="ModelFeatureResult">
        <include refid="selectModelFeatureVo"/>
        where model_id = #{modelId} and delete_time is null
    </select>

    <!-- 根据模型ID查询其所有特性VO -->
    <select id="selectModelFeatureVosByModelId" parameterType="Long" resultMap="ModelFeatureVoResult">
        select id, "key", "value", description
        from a_model_feature
        where model_id = #{modelId} and delete_time is null
    </select>

</mapper>