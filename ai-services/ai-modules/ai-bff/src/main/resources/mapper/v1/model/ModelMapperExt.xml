<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelMapper">

    <!-- ==================== 统计VO结果映射 ==================== -->

    <!-- ModelPerformanceMetricsVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.ModelPerformanceMetricsVo" id="ModelPerformanceMetricsVoResult">
        <result property="totalCalls" column="total_calls"/>
        <result property="successCalls" column="success_calls"/>
        <result property="failedCalls" column="failed_calls"/>
        <result property="timeoutCalls" column="timeout_calls"/>
        <result property="successRate" column="success_rate"/>
        <result property="avgResponseTime" column="avg_response_time"/>
        <result property="minResponseTime" column="min_response_time"/>
        <result property="maxResponseTime" column="max_response_time"/>
        <result property="uniqueUsers" column="unique_users"/>
        <result property="totalInputTokens" column="total_input_tokens"/>
        <result property="totalOutputTokens" column="total_output_tokens"/>
        <result property="avgQph" column="avg_qph"/>
    </resultMap>

    <!-- MemberUsageStatsVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.MemberUsageStatsVo" id="MemberUsageStatsVoResult">
        <result property="totalCalls" column="total_calls"/>
        <result property="successCalls" column="success_calls"/>
        <result property="failedCalls" column="failed_calls"/>
        <result property="timeoutCalls" column="timeout_calls"/>
        <result property="successRate" column="success_rate"/>
        <result property="usedModels" column="used_models"/>
        <result property="totalInputTokens" column="total_input_tokens"/>
        <result property="totalOutputTokens" column="total_output_tokens"/>
        <result property="totalTokens" column="total_tokens"/>
        <result property="avgResponseTime" column="avg_response_time"/>
        <result property="firstCallDate" column="first_call_date"/>
        <result property="lastCallDate" column="last_call_date"/>
    </resultMap>

    <!-- MemberModelUsageStatsVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.MemberModelUsageStatsVo" id="MemberModelUsageStatsVoResult">
        <result property="totalCalls" column="total_calls"/>
        <result property="successCalls" column="success_calls"/>
        <result property="failedCalls" column="failed_calls"/>
        <result property="timeoutCalls" column="timeout_calls"/>
        <result property="successRate" column="success_rate"/>
        <result property="totalInputTokens" column="total_input_tokens"/>
        <result property="totalOutputTokens" column="total_output_tokens"/>
        <result property="totalTokens" column="total_tokens"/>
        <result property="avgResponseTime" column="avg_response_time"/>
        <result property="minResponseTime" column="min_response_time"/>
        <result property="maxResponseTime" column="max_response_time"/>
        <result property="firstCallDate" column="first_call_date"/>
        <result property="lastCallDate" column="last_call_date"/>
    </resultMap>

    <!-- ==================== 会员个性化功能查询 ==================== -->

    <!-- 查询会员收藏的模型列表 -->
    <select id="selectFavoriteModelsByMemberId" resultMap="ModelListVoResult">
        select m.id, m.name, m.code, m.source, m.version,
               m.supports_stream, m.supports_function, m.sort_order, m.description, m.is_enabled,
               c.name as category_name,
               p.provider_name as provider_name
        from a_model m
        inner join a_member_favorite_model mfm on m.id = mfm.model_id
        left join a_model_category c on m.category_id = c.id and c.delete_time is null
        left join a_model_provider p on m.provider_id = p.id
        where mfm.member_id = #{memberId} and mfm.delete_time is null
        and m.is_enabled = true and m.delete_time is null
        order by mfm.create_time desc
    </select>

    <!-- 检查会员是否收藏了指定模型 -->
    <select id="isMemberFavoriteModel" resultType="boolean">
        select case when count(*) > 0 then true else false end
        from a_member_favorite_model
        where member_id = #{memberId} and model_id = #{modelId} and delete_time is null
    </select>

    <!-- 添加会员收藏模型 -->
    <insert id="addMemberFavoriteModel">
        insert into a_member_favorite_model (member_id, model_id, create_time)
        values (#{memberId}, #{modelId}, now())
        on conflict (member_id, model_id) do update set
        delete_time = null,
        update_time = now()
    </insert>

    <!-- 移除会员收藏模型 -->
    <update id="removeMemberFavoriteModel">
        update a_member_favorite_model
        set delete_time = now(),
            update_time = now()
        where member_id = #{memberId} and model_id = #{modelId} and delete_time is null
    </update>

    <!-- 查询会员最近使用的模型列表 -->
    <select id="selectRecentModelsByMemberId" resultMap="ModelListVoResult">
        select distinct m.id, m.name, m.code, m.source, m.version,
               m.supports_stream, m.supports_function, m.sort_order, m.description, m.is_enabled,
               c.name as category_name,
               p.provider_name as provider_name
        from a_model m
        inner join a_model_call_log mcl on m.id = mcl.model_id
        left join a_model_category c on m.category_id = c.id and c.delete_time is null
        left join a_model_provider p on m.provider_id = p.id
        where mcl.member_id = #{memberId}
        and m.is_enabled = true and m.delete_time is null
        and mcl.request_time >= current_date - interval '30 days'
        and mcl.status = 1  -- 1-成功 (字典: model_call_status, 枚举: ModelCallStatusEnum.SUCCESS)
        order by mcl.request_time desc
        limit #{limit}
    </select>

    <!-- 查询为会员推荐的模型列表 -->
    <select id="selectRecommendedModelsByMemberId" resultMap="ModelListVoResult">
        select m.id, m.name, m.code, m.source, m.version,
               m.supports_stream, m.supports_function, m.sort_order, m.description, m.is_enabled,
               c.name as category_name,
               p.provider_name as provider_name,
               coalesce(usage_stats.call_count, 0) as popularity_score
        from a_model m
        left join a_model_category c on m.category_id = c.id and c.delete_time is null
        left join a_model_provider p on m.provider_id = p.id
        left join (
            select model_id, count(*) as call_count
            from a_model_call_log
            where request_time >= current_date - interval '7 days'
            and status = 1  -- 1-成功 (字典: model_call_status, 枚举: ModelCallStatusEnum.SUCCESS)
            group by model_id
        ) usage_stats on m.id = usage_stats.model_id
        left join a_model_visibility mv on m.id = mv.model_id and mv.delete_time is null and mv.is_enabled = true
        where m.is_enabled = true and m.delete_time is null
        and (mv.id is null or (
            (mv.visibility_type = 2 and mv.reference_id = #{memberId})
            or (mv.visibility_type = 1 and mv.reference_id in (
                select role_id from a_member_role where member_id = #{memberId} and delete_time is null
            ))
            or (mv.visibility_type = 3 and mv.reference_id in (
                select country_id from a_member where id = #{memberId}
            ))
        ))
        order by popularity_score desc, m.sort_order asc, m.create_time desc
        limit #{limit}
    </select>

    <!-- ==================== 统计和监控查询 ==================== -->

    <!-- 查询模型性能指标 -->
    <select id="selectModelPerformanceMetrics" resultMap="ModelPerformanceMetricsVoResult">
        select
            count(*) as total_calls,
            count(case when status = 1 then 1 end) as success_calls,  -- 1-成功 (ModelCallStatusEnum.SUCCESS)
            count(case when status = 2 then 1 end) as failed_calls,   -- 2-失败 (ModelCallStatusEnum.FAILED)
            count(case when status = 4 then 1 end) as timeout_calls,  -- 4-超时 (ModelCallStatusEnum.TIMEOUT)
            round(count(case when status = 1 then 1 end) * 100.0 / nullif(count(*), 0), 2) as success_rate,
            round(avg(case when status = 1 then response_time end), 2) as avg_response_time,
            round(min(case when status = 1 then response_time end), 2) as min_response_time,
            round(max(case when status = 1 then response_time end), 2) as max_response_time,
            count(distinct member_id) as unique_users,
            sum(case when status = 1 then input_tokens else 0 end) as total_input_tokens,
            sum(case when status = 1 then output_tokens else 0 end) as total_output_tokens,
            round(count(*) * 1.0 / nullif(extract(epoch from (max(request_time) - min(request_time))), 0) * 3600, 2) as avg_qph
        from a_model_call_log
        where model_id = #{modelId}
        and request_time >= current_date - interval '7 days'
    </select>

    <!-- 查询会员使用统计 -->
    <select id="selectMemberUsageStats" resultMap="MemberUsageStatsVoResult">
        select
            count(*) as total_calls,
            count(case when status = 1 then 1 end) as success_calls,  -- 1-成功 (ModelCallStatusEnum.SUCCESS)
            count(case when status = 2 then 1 end) as failed_calls,   -- 2-失败 (ModelCallStatusEnum.FAILED)
            count(case when status = 4 then 1 end) as timeout_calls,  -- 4-超时 (ModelCallStatusEnum.TIMEOUT)
            round(count(case when status = 1 then 1 end) * 100.0 / nullif(count(*), 0), 2) as success_rate,
            count(distinct model_id) as used_models,
            sum(case when status = 1 then input_tokens else 0 end) as total_input_tokens,
            sum(case when status = 1 then output_tokens else 0 end) as total_output_tokens,
            sum(case when status = 1 then total_tokens else 0 end) as total_tokens,
            round(avg(case when status = 1 then response_time end), 2) as avg_response_time,
            date_trunc('day', min(request_time)) as first_call_date,
            date_trunc('day', max(request_time)) as last_call_date
        from a_model_call_log
        where member_id = #{memberId}
        and request_time >= current_date - interval '30 days'
    </select>

    <!-- 查询会员对特定模型的使用统计 -->
    <select id="selectMemberModelUsageStats" resultMap="MemberModelUsageStatsVoResult">
        select
            count(*) as total_calls,
            count(case when status = 1 then 1 end) as success_calls,  -- 1-成功 (ModelCallStatusEnum.SUCCESS)
            count(case when status = 2 then 1 end) as failed_calls,   -- 2-失败 (ModelCallStatusEnum.FAILED)
            count(case when status = 4 then 1 end) as timeout_calls,  -- 4-超时 (ModelCallStatusEnum.TIMEOUT)
            round(count(case when status = 1 then 1 end) * 100.0 / nullif(count(*), 0), 2) as success_rate,
            sum(case when status = 1 then input_tokens else 0 end) as total_input_tokens,
            sum(case when status = 1 then output_tokens else 0 end) as total_output_tokens,
            sum(case when status = 1 then total_tokens else 0 end) as total_tokens,
            round(avg(case when status = 1 then response_time end), 2) as avg_response_time,
            round(min(case when status = 1 then response_time end), 2) as min_response_time,
            round(max(case when status = 1 then response_time end), 2) as max_response_time,
            date_trunc('day', min(request_time)) as first_call_date,
            date_trunc('day', max(request_time)) as last_call_date
        from a_model_call_log
        where member_id = #{memberId} and model_id = #{modelId}
        and request_time >= current_date - interval '30 days'
    </select>

</mapper>
