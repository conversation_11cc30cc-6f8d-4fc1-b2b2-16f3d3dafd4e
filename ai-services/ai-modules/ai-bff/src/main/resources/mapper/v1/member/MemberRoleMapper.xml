<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.member.MemberRoleMapper">

    <select id="selectRoleIdsByMemberId" parameterType="Long" resultType="java.lang.Long">
        select role_id from a_member_role
        where member_id = #{memberId} and delete_time is null
    </select>

    <select id="checkMemberHasRole" resultType="java.lang.Integer">
        select count(1) from a_member_role
        where member_id = #{memberId} and role_id = #{roleId} and delete_time is null
    </select>

    <insert id="batchInsertMemberRole">
        insert into a_member_role(member_id, role_id, create_time, update_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.memberId}, #{item.roleId}, now(), now())
        </foreach>
    </insert>

    <update id="softDeleteMemberRole">
        update a_member_role
        set delete_time = now()
        where member_id = #{memberId} and role_id = #{roleId}
    </update>

</mapper> 