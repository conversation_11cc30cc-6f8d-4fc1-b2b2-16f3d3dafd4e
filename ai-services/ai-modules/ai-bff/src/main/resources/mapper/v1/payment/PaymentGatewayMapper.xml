<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.payment.PaymentGatewayMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.payment.PaymentGateway" id="PaymentGatewayResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="configParams"    column="config_params"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPaymentGatewayVo">
        select id, name, code, logo_url, config_params, status, sort_order, description, delete_time, create_by, update_by, create_time, update_time from a_payment_gateway
    </sql>

    <select id="selectPaymentGatewayById" parameterType="Long" resultMap="PaymentGatewayResult">
        <include refid="selectPaymentGatewayVo"/>
        where id = #{id} and delete_time is null
    </select>

    <select id="selectPaymentGatewayByCode" parameterType="String" resultMap="PaymentGatewayResult">
        <include refid="selectPaymentGatewayVo"/>
        where code = #{code} and delete_time is null
    </select>

    <select id="selectAvailablePaymentGateways" resultMap="PaymentGatewayResult">
        <include refid="selectPaymentGatewayVo"/>
        where status = 1 and delete_time is null
        order by sort_order
    </select>

    <select id="selectPaymentGatewaysByCountry" parameterType="Long" resultMap="PaymentGatewayResult">
        <include refid="selectPaymentGatewayVo"/> pg
        join a_payment_gateway_country pgc on pg.id = pgc.gateway_id
        where pgc.country_id = #{countryId}
        and pg.status = 1
        and pg.delete_time is null
        and pgc.delete_time is null
        order by pg.sort_order
    </select>

</mapper> 