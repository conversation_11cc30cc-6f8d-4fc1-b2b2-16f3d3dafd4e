<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingTransactionMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingTransaction" id="BillingTransactionResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="type" column="type"/>
        <result property="amount" column="amount"/>
        <result property="currencyId" column="currency_id"/>
        <result property="referenceId" column="reference_id"/>
        <result property="description" column="description"/>
        <result property="transactionTime" column="transaction_time"/>
    </resultMap>

    <sql id="selectBillingTransactionVo">
        select id, member_id, type, amount, currency_id, reference_id, description, transaction_time
        from a_billing_transaction
    </sql>

    <select id="selectTransactionsByMemberId" parameterType="Long" resultMap="BillingTransactionResult">
        <include refid="selectBillingTransactionVo"/>
        where member_id = #{memberId} and delete_time is null
        order by transaction_time desc
    </select>

    <insert id="insertTransaction" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingTransaction" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time, create_time, update_time)
        values (#{memberId}, #{type}, #{amount}, #{currencyId}, #{referenceId}, #{description}, #{transactionTime}, now(), now())
    </insert>

</mapper> 