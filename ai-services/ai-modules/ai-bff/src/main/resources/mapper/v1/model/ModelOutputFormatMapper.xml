<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelOutputFormatMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.ModelOutputFormat" id="ModelOutputFormatResult">
        <result property="id" column="id"/>
        <result property="modelId" column="model_id"/>
        <result property="outputType" column="output_type"/>
        <result property="supportsStream" column="supports_stream"/>
        <result property="maxTokens" column="max_tokens"/>
        <result property="description" column="description"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- ModelOutputFormatVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.ModelOutputFormatVo" id="ModelOutputFormatVoResult">
        <result property="id" column="id"/>
        <result property="outputType" column="output_type"/>
        <result property="supportsStream" column="supports_stream"/>
        <result property="maxTokens" column="max_tokens"/>
        <result property="description" column="description"/>
    </resultMap>

    <sql id="selectModelOutputFormatVo">
        select id, model_id, output_type, supports_stream, max_tokens, description, delete_time, create_time, update_time
        from a_model_output_format
    </sql>

    <select id="selectModelOutputFormatsByModelId" parameterType="Long" resultMap="ModelOutputFormatResult">
        <include refid="selectModelOutputFormatVo"/>
        where model_id = #{modelId} and delete_time is null
    </select>

    <!-- 根据模型ID查询其支持的所有输出格式VO -->
    <select id="selectModelOutputFormatVosByModelId" parameterType="Long" resultMap="ModelOutputFormatVoResult">
        select id, output_type, supports_stream, max_tokens, description
        from a_model_output_format
        where model_id = #{modelId} and delete_time is null
    </select>

</mapper>