<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.knowledge.KnowledgeMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.knowledge.Knowledge" id="KnowledgeResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="name" column="name"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="description" column="description"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="embeddingModelId" column="embedding_model_id"/>
        <result property="permissionType" column="permission_type"/>
    </resultMap>

    <sql id="selectKnowledgeVo">
        select id, member_id, name, sort_order, description, icon_url, embedding_model_id, permission_type
        from a_knowledge
    </sql>

    <select id="selectKnowledgesByMemberId" parameterType="Long" resultMap="KnowledgeResult">
        <include refid="selectKnowledgeVo"/>
        where member_id = #{memberId} and delete_time is null
        order by sort_order desc, create_time desc
    </select>

    <select id="selectKnowledgeById" parameterType="Long" resultMap="KnowledgeResult">
        <include refid="selectKnowledgeVo"/>
        where id = #{id} and delete_time is null
    </select>

    <insert id="insertKnowledge" parameterType="ai.showlab.bff.entity.domain.v1.knowledge.Knowledge" useGeneratedKeys="true" keyProperty="id">
        insert into a_knowledge (member_id, name, sort_order, description, icon_url, embedding_model_id, permission_type, create_time, update_time)
        values (#{memberId}, #{name}, #{sortOrder}, #{description}, #{iconUrl}, #{embeddingModelId}, #{permissionType}, now(), now())
    </insert>

    <update id="updateKnowledge" parameterType="ai.showlab.bff.entity.domain.v1.knowledge.Knowledge">
        update a_knowledge
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="iconUrl != null and iconUrl != ''">icon_url = #{iconUrl},</if>
            <if test="embeddingModelId != null">embedding_model_id = #{embeddingModelId},</if>
            <if test="permissionType != null">permission_type = #{permissionType},</if>
            update_time = now(),
        </trim>
        where id = #{id} and member_id = #{memberId} and delete_time is null
    </update>

    <update id="softDeleteKnowledge" parameterType="Long">
        update a_knowledge
        set delete_time = now()
        where id = #{id} and member_id = #{memberId}
    </update>

</mapper> 