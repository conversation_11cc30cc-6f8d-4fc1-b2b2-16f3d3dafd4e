<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.model.ModelMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.model.Model" id="ModelResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="source" column="source"/>
        <result property="categoryId" column="category_id"/>
        <result property="providerId" column="provider_id"/>
        <result property="version" column="version"/>
        <result property="apiEndpoint" column="api_endpoint"/>
        <result property="invokeMethod" column="invoke_method"/>
        <result property="supportsStream" column="supports_stream"/>
        <result property="supportsFunction" column="supports_function"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="description" column="description"/>
        <result property="isEnabled" column="is_enabled"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="artifactPath" column="artifact_path"/>
        <result property="artifactType" column="artifact_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- ModelListVo 结果映射 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.ModelListVo" id="ModelListVoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="source" column="source"/>
        <result property="version" column="version"/>
        <result property="supportsStream" column="supports_stream"/>
        <result property="supportsFunction" column="supports_function"/>
        <result property="description" column="description"/>
        <result property="categoryName" column="category_name"/>
        <result property="providerName" column="provider_name"/>
    </resultMap>

    <!-- ModelVo基础信息结果映射（不包含一对多关联） -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.ModelVo" id="ModelVoBaseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="source" column="source"/>
        <result property="version" column="version"/>
        <result property="supportsStream" column="supports_stream"/>
        <result property="supportsFunction" column="supports_function"/>
        <result property="description" column="description"/>
        <!-- 分类信息 -->
        <association property="category" javaType="ai.showlab.bff.entity.vo.v1.ModelCategoryVo">
            <result property="id" column="category_id"/>
            <result property="code" column="category_code"/>
            <result property="name" column="category_name"/>
        </association>
        <!-- 供应商信息 -->
        <association property="provider" javaType="ai.showlab.bff.entity.vo.v1.ModelProviderVo">
            <result property="id" column="provider_id"/>
            <result property="providerName" column="provider_name"/>
            <result property="providerKey" column="provider_key"/>
            <result property="channelType" column="channel_type"/>
            <result property="description" column="provider_description"/>
        </association>
    </resultMap>

    <sql id="selectModelVo">
        select id, name, code, source, category_id, provider_id, version, api_endpoint, invoke_method,
        supports_stream, supports_function, description, is_enabled, delete_time,
               artifact_path, artifact_type, create_time, update_time
        from a_model
    </sql>

    <!-- 查询ModelListVo的SQL片段，包含JOIN关联查询 -->
    <sql id="selectModelListVo">
        select m.id, m.name, m.code, m.source, m.version,
        m.supports_stream, m.supports_function, m.description,
        c.name as category_name,
        p.provider_name as provider_name
        from a_model m
        left join a_model_category c on m.category_id = c.id and c.delete_time is null
        left join a_model_provider p on m.provider_id = p.id
    </sql>

    <!-- 查询ModelVo基础信息的SQL片段，包含分类和供应商信息 -->
    <sql id="selectModelVoBase">
        select m.id, m.name, m.code, m.source, m.version,
        m.supports_stream, m.supports_function, m.description,
        m.category_id, c.code as category_code, c.name as category_name,
        m.provider_id, p.provider_name, p.provider_key, p.channel_type,
        p.description as provider_description
        from a_model m
        left join a_model_category c on m.category_id = c.id and c.delete_time is null
        left join a_model_provider p on m.provider_id = p.id
    </sql>

    <select id="selectModelById" parameterType="Long" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null and id = #{id}
    </select>

    <select id="selectModelByCode" parameterType="String" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null and code = #{code}
    </select>

    <select id="selectModelsByCategoryId" parameterType="Long" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null and category_id = #{categoryId}
        order by sort_order asc
    </select>

    <select id="selectModelsByProviderId" parameterType="Long" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null and provider_id = #{providerId}
        order by sort_order asc
    </select>

    <select id="selectAllModels" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null
        order by sort_order asc
    </select>

    <!-- 根据条件分页查询模型列表 -->
    <select id="selectModelListByCondition" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        <where>
            is_enabled = true and delete_time is null
            <if test="param.categoryId != null">
                and category_id = #{param.categoryId}
            </if>
            <if test="param.providerId != null">
                and provider_id = #{param.providerId}
            </if>
            <if test="param.source != null">
                and source = #{param.source}
            </if>
            <if test="param.supportsStream != null">
                and supports_stream = #{param.supportsStream}
            </if>
            <if test="param.supportsFunction != null">
                and supports_function = #{param.supportsFunction}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and (name like concat('%', #{param.keyword}, '%')
                or description like concat('%', #{param.keyword}, '%'))
            </if>
        </where>
        order by sort_order asc, create_time desc
        limit #{param.pageSize} offset #{param.pageSize} * (#{param.pageNum} - 1)
    </select>

    <!-- 根据条件统计模型总数 -->
    <select id="countModelsByCondition" resultType="long">
        select count(*)
        from a_model
        <where>
            is_enabled = true and delete_time is null
            <if test="param.categoryId != null">
                and category_id = #{param.categoryId}
            </if>
            <if test="param.providerId != null">
                and provider_id = #{param.providerId}
            </if>
            <if test="param.source != null">
                and source = #{param.source}
            </if>
            <if test="param.supportsStream != null">
                and supports_stream = #{param.supportsStream}
            </if>
            <if test="param.supportsFunction != null">
                and supports_function = #{param.supportsFunction}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and (name like concat('%', #{param.keyword}, '%')
                or description like concat('%', #{param.keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 根据条件分页查询模型列表VO（直接返回VO，包含关联信息） -->
    <select id="selectModelListVoByCondition" resultMap="ModelListVoResult">
        <include refid="selectModelListVo"/>
        <where>
            m.is_enabled = true and m.delete_time is null
            <if test="param.categoryId != null">
                and m.category_id = #{param.categoryId}
            </if>
            <if test="param.providerId != null">
                and m.provider_id = #{param.providerId}
            </if>
            <if test="param.source != null">
                and m.source = #{param.source}
            </if>
            <if test="param.supportsStream != null">
                and m.supports_stream = #{param.supportsStream}
            </if>
            <if test="param.supportsFunction != null">
                and m.supports_function = #{param.supportsFunction}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and (m.name like concat('%', #{param.keyword}, '%')
                or m.description like concat('%', #{param.keyword}, '%'))
            </if>
        </where>
        order by m.sort_order asc, m.create_time desc
        limit #{param.pageSize} offset #{param.pageSize} * (#{param.pageNum} - 1)
    </select>

    <!-- 查询热门模型列表 -->
    <select id="selectPopularModels" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null
        order by sort_order asc, create_time desc
        limit #{limit}
    </select>

    <!-- 查询热门模型列表VO（直接返回VO，包含关联信息） -->
    <select id="selectPopularModelsVo" resultMap="ModelListVoResult">
        <include refid="selectModelListVo"/>
        where m.is_enabled = true and m.delete_time is null
        order by m.sort_order asc, m.create_time desc
        limit #{limit}
    </select>

    <!-- 根据模型ID查询完整的模型信息（包含关联数据） -->
    <select id="selectModelWithDetailsById" parameterType="Long" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null and id = #{id}
    </select>

    <!-- 根据模型编码查询完整的模型信息（包含关联数据） -->
    <select id="selectModelWithDetailsByCode" parameterType="String" resultMap="ModelResult">
        <include refid="selectModelVo"/>
        where is_enabled = true and delete_time is null and code = #{code}
    </select>

    <!-- 根据ID查询模型详情VO基础信息（不包含特性和输出格式） -->
    <select id="selectModelVoBaseById" parameterType="Long" resultMap="ModelVoBaseResult">
        <include refid="selectModelVoBase"/>
        where m.is_enabled = true and m.delete_time is null and m.id = #{id}
    </select>

    <!-- 根据编码查询模型详情VO基础信息（不包含特性和输出格式） -->
    <select id="selectModelVoBaseByCode" parameterType="String" resultMap="ModelVoBaseResult">
        <include refid="selectModelVoBase"/>
        where m.is_enabled = true and m.delete_time is null and m.code = #{code}
    </select>

    <!-- ==================== 会员权限相关查询 ==================== -->

    <!-- 根据条件统计会员可访问的模型总数 -->
    <select id="countAccessibleModelsByCondition" resultType="long">
        select count(distinct m.id)
        from a_model m
        left join a_model_visibility mv on m.id = mv.model_id and mv.delete_time is null and mv.is_enabled = true
        <where>
            m.is_enabled = true and m.delete_time is null
            and (mv.id is null or (
                (mv.visibility_type = 2 and mv.reference_id = #{memberId})
                or (mv.visibility_type = 1 and mv.reference_id in (
                    select role_id from a_member_role where member_id = #{memberId} and delete_time is null
                ))
                or (mv.visibility_type = 3 and mv.reference_id in (
                    select country_id from a_member where id = #{memberId}
                ))
            ))
            <if test="param.categoryId != null">
                and m.category_id = #{param.categoryId}
            </if>
            <if test="param.providerId != null">
                and m.provider_id = #{param.providerId}
            </if>
            <if test="param.source != null">
                and m.source = #{param.source}
            </if>
            <if test="param.supportsStream != null">
                and m.supports_stream = #{param.supportsStream}
            </if>
            <if test="param.supportsFunction != null">
                and m.supports_function = #{param.supportsFunction}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and (m.name like concat('%', #{param.keyword}, '%')
                     or m.description like concat('%', #{param.keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 根据条件分页查询会员可访问的模型列表VO -->
    <select id="selectAccessibleModelListVoByCondition" resultMap="ModelListVoResult">
        select distinct m.id, m.name, m.code, m.source, m.version,
               m.supports_stream, m.supports_function, m.sort_order, m.description, m.is_enabled,
               c.name as category_name,
               p.provider_name as provider_name
        from a_model m
        left join a_model_category c on m.category_id = c.id and c.delete_time is null
        left join a_model_provider p on m.provider_id = p.id
        left join a_model_visibility mv on m.id = mv.model_id and mv.delete_time is null and mv.is_enabled = true
        <where>
            m.is_enabled = true and m.delete_time is null
            and (mv.id is null or (
                (mv.visibility_type = 2 and mv.reference_id = #{memberId})
                or (mv.visibility_type = 1 and mv.reference_id in (
                    select role_id from a_member_role where member_id = #{memberId} and delete_time is null
                ))
                or (mv.visibility_type = 3 and mv.reference_id in (
                    select country_id from a_member where id = #{memberId}
                ))
            ))
            <if test="param.categoryId != null">
                and m.category_id = #{param.categoryId}
            </if>
            <if test="param.providerId != null">
                and m.provider_id = #{param.providerId}
            </if>
            <if test="param.source != null">
                and m.source = #{param.source}
            </if>
            <if test="param.supportsStream != null">
                and m.supports_stream = #{param.supportsStream}
            </if>
            <if test="param.supportsFunction != null">
                and m.supports_function = #{param.supportsFunction}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and (m.name like concat('%', #{param.keyword}, '%')
                     or m.description like concat('%', #{param.keyword}, '%'))
            </if>
        </where>
        order by m.sort_order asc, m.create_time desc
        limit #{param.pageSize} offset #{param.pageSize} * (#{param.pageNum} - 1)
    </select>

    <!-- 检查会员是否有权限访问指定模型 -->
    <select id="checkMemberModelAccess" resultType="boolean">
        select case when count(*) > 0 then true else false end
        from a_model m
        left join a_model_visibility mv on m.id = mv.model_id and mv.delete_time is null and mv.is_enabled = true
        where m.id = #{modelId} and m.is_enabled = true and m.delete_time is null
        and (mv.id is null or (
            (mv.visibility_type = 2 and mv.reference_id = #{memberId})
            or (mv.visibility_type = 1 and mv.reference_id in (
                select role_id from a_member_role where member_id = #{memberId} and delete_time is null
            ))
            or (mv.visibility_type = 3 and mv.reference_id in (
                select country_id from a_member where id = #{memberId}
            ))
        ))
    </select>

    <update id="updateModelDescription" parameterType="ai.showlab.bff.entity.domain.v1.model.Model">
        update a_model
        set description = #{description},
            update_time = now()
        where id = #{id} and delete_time is null
    </update>

</mapper>