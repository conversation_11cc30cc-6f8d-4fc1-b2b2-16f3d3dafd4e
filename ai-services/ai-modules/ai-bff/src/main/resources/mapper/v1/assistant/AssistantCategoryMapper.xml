<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.assistant.AssistantCategoryMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.assistant.AssistantCategory" id="AssistantCategoryResult">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectAssistantCategoryVo">
        select id, pid, type, name, description, sort_order
        from a_assistant_category
    </sql>

    <select id="selectAllCategories" resultMap="AssistantCategoryResult">
        <include refid="selectAssistantCategoryVo"/>
        where delete_time is null
        order by sort_order asc
    </select>

    <select id="selectCategoriesByPid" parameterType="Long" resultMap="AssistantCategoryResult">
        <include refid="selectAssistantCategoryVo"/>
        where pid = #{pid} and delete_time is null
        order by sort_order asc
    </select>

</mapper> 