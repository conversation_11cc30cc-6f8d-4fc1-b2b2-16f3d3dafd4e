package ai.showlab.bff.controller.v1;

import ai.showlab.bff.AiBffApp;
import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.bo.LoginMember;
import ai.showlab.bff.entity.vo.v1.MemberInfoVo;
import ai.showlab.bff.service.common.IDuplicateSubmissionTokenService;
import ai.showlab.bff.service.v1.member.IMemberService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Collections;

/**
 * MemberController 个人信息相关集成测试
 * 包含了所有与用户个人信息（获取、更新昵称、头像）相关的测试
 * <AUTHOR>
 */
@SpringBootTest(classes = AiBffApp.class)
@AutoConfigureMockMvc
@TestPropertySource(properties = {"spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration,org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration", "spring.cloud.openfeign.cache.enabled=false"})
@ExtendWith(MockitoExtension.class)
@WebAppConfiguration
public class MemberProfileControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private IMemberService memberService;
    @MockBean
    private IDuplicateSubmissionTokenService duplicateSubmissionTokenService;
    @MockBean
    private JwtTokenProvider jwtTokenProvider;
    @MockBean
    private RedisTemplate<String, Object> redisTemplate;
    @MockBean
    private RedisConnectionFactory redisConnectionFactory;

    private ObjectMapper objectMapper = new ObjectMapper();

    private MemberInfoVo memberInfoVo;
    private Long memberId;
    private String username;
    private String jwtToken;
    private String avatarUrl;
    private String nickname;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        memberId = 1L;
        username = "testuser";
        jwtToken = "mockJwtToken";
        avatarUrl = "http://example.com/avatar.jpg";
        nickname = "新测试用户";

        // Mock SecurityContextHolder
        LoginMember mockLoginMember = new LoginMember(memberId, username, jwtToken, "member");
        Authentication authentication = new UsernamePasswordAuthenticationToken(mockLoginMember, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // Initialize memberInfoVo (as it's used in some tests and was present in original file)
        memberInfoVo = new MemberInfoVo();
        memberInfoVo.setId(memberId);
        memberInfoVo.setUsername(username);
        memberInfoVo.setNickname("Test User");
    }


} 