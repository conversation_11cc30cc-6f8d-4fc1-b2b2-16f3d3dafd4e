package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.domain.v1.member.Member;
import ai.showlab.bff.entity.domain.v1.member.MemberAuth;
import ai.showlab.bff.mapper.v1.member.MemberAuthMapper;
import ai.showlab.bff.service.common.INotificationService;
import ai.showlab.bff.service.v1.member.IMemberService;
import ai.showlab.common.protocol.enums.AuthTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.OffsetDateTime;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * MemberAuthServiceImpl 单元测试
 *
 * <AUTHOR>
 */
public class MemberAuthServiceImplTest {

    @InjectMocks
    private MemberAuthServiceImpl memberAuthService;

    @Mock
    private MemberAuthMapper memberAuthMapper;
    @Mock
    private BCryptPasswordEncoder passwordEncoder;
    @Mock
    private IMemberService memberService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private JwtTokenProvider jwtTokenProvider;
    @Mock
    private INotificationService notificationService;

    // 用于模拟 RedisTemplate.opsForValue()
    @Mock
    private ValueOperations<String, Object> valueOperations;

    private String username;
    private String password;
    private String email;
    private String phone;
    private Long memberId;
    private String token;
    private String identifier;
    private String resetCode;
    private String newPassword;
    private String oldPassword;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // 确保 valueOperations 被 mock 并且当调用 opsForValue() 时返回它
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        username = "testuser";
        password = "Password123!";
        email = "<EMAIL>";
        phone = "13812345678";
        memberId = 1L;
        token = "mockJwtToken";
        identifier = "testuser";
        resetCode = "123456";
        newPassword = "NewPassword123!";
        oldPassword = "OldPassword123!";

        // 默认模拟，确保测试通过，除非特定测试需要不同的行为
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(jwtTokenProvider.generateToken(anyString(), anyMap())).thenReturn(token);
        when(jwtTokenProvider.getExpirationDateFromToken(anyString())).thenReturn(new Date(System.currentTimeMillis() + 3600 * 1000)); // 1小时后过期

        // 模拟 memberService 默认行为
        when(memberService.isUsernameExists(anyString())).thenReturn(false);
        when(memberService.isEmailExists(anyString())).thenReturn(false);
        when(memberService.isPhoneExists(anyString())).thenReturn(false);
        when(memberService.createMember(any(Member.class))).thenReturn(memberId);
        doNothing().when(memberService).updateMemberLastLoginInfo(anyLong(), anyString(), any(OffsetDateTime.class));

        // 模拟 memberAuthMapper 默认行为
        when(memberAuthMapper.selectMemberAuthByIdentifier(anyInt(), anyString())).thenReturn(null);
        doNothing().when(memberAuthMapper).insertMemberAuth(any(MemberAuth.class));
        // 模拟 selectAuthForVerification 返回一个 MemberAuth 实例，并设置其属性
        MemberAuth mockAuthForVerification = new MemberAuth();
        mockAuthForVerification.setMemberId(memberId);
        mockAuthForVerification.setAuthType(AuthTypeEnum.PASSWORD.getCode());
        mockAuthForVerification.setIdentifier(username);
        mockAuthForVerification.setCredential("encodedPassword");
        when(memberAuthMapper.selectAuthForVerification(anyInt(), anyString())).thenReturn(mockAuthForVerification);

        // 模拟 notificationService
        doNothing().when(notificationService).sendEmailCode(anyString(), anyString());
        doNothing().when(notificationService).sendSmsCode(anyString(), anyString());
    }

} 