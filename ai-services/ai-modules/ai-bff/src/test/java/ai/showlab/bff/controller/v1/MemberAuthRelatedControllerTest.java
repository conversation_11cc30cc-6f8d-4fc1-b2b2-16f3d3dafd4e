package ai.showlab.bff.controller.v1;

import ai.showlab.bff.AiBffApp;
import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.bo.LoginMember;
import ai.showlab.bff.service.common.IDuplicateSubmissionTokenService;
import ai.showlab.bff.service.v1.member.IMemberAuthService;
import ai.showlab.bff.service.v1.member.IMemberService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Collections;

/**
 * MemberController 认证相关集成测试
 * 包含了所有与用户认证（注册、登录、注销、密码修改/重置以及获取防重复提交令牌）相关的测试
 * <AUTHOR>
 */
@SpringBootTest(classes = AiBffApp.class)
@AutoConfigureMockMvc
@TestPropertySource(properties = {"spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration,org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration", "spring.cloud.openfeign.cache.enabled=false"})
@ExtendWith(MockitoExtension.class)
@WebAppConfiguration
public class MemberAuthRelatedControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private IMemberAuthService memberAuthService;
    @MockBean
    private IMemberService memberService;
    @MockBean
    private IDuplicateSubmissionTokenService duplicateSubmissionTokenService;
    @MockBean
    private JwtTokenProvider jwtTokenProvider;
    @MockBean
    private RedisTemplate<String, Object> redisTemplate;
    @MockBean
    private RedisConnectionFactory redisConnectionFactory;

    private ObjectMapper objectMapper = new ObjectMapper();

    private String username;
    private String password;
    private String email;
    private String phone;
    private Long memberId;
    private String jwtToken;
    private String identifier;
    private String resetCode;
    private String newPassword;
    private String oldPassword;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        username = "testuser";
        password = "Password123!";
        email = "<EMAIL>";
        phone = "13800138000";
        memberId = 1L;
        jwtToken = "mockJwtToken";
        identifier = "testuser";
        resetCode = "123456";
        newPassword = "NewPassword123!";
        oldPassword = "OldPassword123!";

        // Mock SecurityContextHolder
        LoginMember mockLoginMember = new LoginMember(memberId, username, jwtToken, "member");
        Authentication authentication = new UsernamePasswordAuthenticationToken(mockLoginMember, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }


} 