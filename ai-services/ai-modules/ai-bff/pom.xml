<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>ai.showlab</groupId>
        <artifactId>ai-modules</artifactId>
        <version>1.0.0</version>
    </parent>
    <name>ai-bff</name>
    <artifactId>ai-bff</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>

        <!--<dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-core</artifactId>
        </dependency>-->

        <!--<dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-api-system</artifactId>
        </dependency>-->

        <dependency>
            <groupId>ai.showlab</groupId>
            <artifactId>ai-common-core</artifactId>
            <version>${aishowlab.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>ai.showlab</groupId>
            <artifactId>ai-api-system</artifactId>
            <version>${aishowlab.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>ai.showlab</groupId>
            <artifactId>ai-common-protocol</artifactId>
            <version>${aishowlab.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>ai.showlab</groupId>
            <artifactId>ai-common-constants</artifactId>
        </dependency>

        <!-- 停用 tomcat，改用 undertow -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!-- Spring Cloud OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Spring AOP -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Spring Cloud nacos Discovery -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-logback-adapter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-log4j2-adapter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-logback-adapter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-log4j2-adapter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 移除过时的 JUnit 3 依赖 -->
        <!-- <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency> -->

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>

        <!-- Bean Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <!-- 排除 JUnit 4 -->
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 添加 AssertJ 用于更流畅的断言 -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Dynamic DataSource -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>4.3.0</version>
        </dependency>

        <!-- MyBatis -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>3.0.3</version>
        </dependency>

        <!-- PageHelper -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-datasource</artifactId>
        </dependency>

        <!-- RuoYi Common Redis-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations-jakarta</artifactId>
            <version>2.2.28</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
        </dependency>

    </dependencies>


    <build>
        <!--1. 名称-->
        <finalName>${project.artifactId}</finalName>
        <!--2. 项目资源路径-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <!--3. 测试资源路径-->
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
        <!--4. 打包插件引用-->
        <plugins>
            <!--4.1 maven打包插件-->
            <plugin>
                <!--4.1.1 maven打包插件版本引用-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
                <!--4.1.1 配置项-->
                <configuration>
                    <!--******* 配置要打成jar包的文件，此处只将class文件打成jar包-->
                    <includes>
                        <include>**/**.class</include>
                    </includes>
                    <archive>
                        <!-- 生成的jar中，包含pom.xml和pom.properties这两个文件 -->
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <!-- 为依赖包添加路径, 这些路径会写在MANIFEST文件的Class-Path下 -->
                            <addClasspath>true</addClasspath>
                            <!-- 这个jar所依赖的jar包添加classPath的时候的前缀，
                            如果这个jar本身和依赖包在同一级目录，则不需要添加 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- jar启动入口类 -->
                            <mainClass>ai.showlab.bff.AiBffApp</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!-- 在Class-Path下添加配置文件的路径 -->
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 输出路径 -->
                    <outputDirectory>${project.build.directory}/release</outputDirectory>
                </configuration>
            </plugin>
            <!--4.2 maven打包插件,将依赖的jar打包至项目指定路径-->
            <plugin>
                <!--4.2.1 声明插件dependency引用-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <!--4.2.2 执行配置-->
                <executions>
                    <execution>
                        <id>copy</id>
                        <!--4.2.2.1 打包操作-->
                        <phase>package</phase>
                        <goals>
                            <!--4.2.2.1.1 目标：复制依赖jar-->
                            <goal>copy-dependencies</goal>
                        </goals>
                        <!--4.2.2.1 打包配置，配置jar输出路径-->
                        <configuration>
                            <outputDirectory>${project.build.directory}/release/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--4.3 maven打包插件,将依赖的项目配置文件打包至项目指定路径-->
            <plugin>
                <!--4.3.1 声明使用maven打包插件，分离配置文件插件-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <!--4.3.2 执行配置-->
                <executions>
                    <execution>
                        <!--4.3.2.1 id:复制配置文件-->
                        <id>copy-resources</id>
                        <!--4.3.2.2 打包操作-->
                        <phase>package</phase>
                        <goals>
                            <!--4.3.2.3 目标：复制配置文件-->
                            <goal>copy-resources</goal>
                        </goals>
                        <!--4.3.2.4 打包配置，配置class输出路径-->
                        <configuration>
                            <!--4.3.2.4.1 配置文件输出路径-->
                            <outputDirectory>${project.build.directory}/release</outputDirectory>
                            <!--4.3.2.4.2 声明配置文件引用路径-->
                            <resources>
                                <resource>
                                    <!--来源路径-->
                                    <directory>${basedir}/src/main/resources</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.5</version> <!-- 更新到最新版本 -->
                <configuration>
                    <argLine>
                        -Dlogback.configurationFile=src/test/resources/logback-test.xml
                        -Dnacos.logging.default.config.enabled=false
                    </argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
