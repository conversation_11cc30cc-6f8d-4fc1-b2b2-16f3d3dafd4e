<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.system.mapper.MemberPermissionMapper">
    
    <resultMap type="ai.showlab.system.domain.MemberPermission" id="MemberPermissionResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="permissionId"    column="permission_id"    />
        <result property="isGranted"    column="is_granted"    />
        <result property="scope"    column="scope"    />
        <result property="validFrom"    column="valid_from"    />
        <result property="validTo"    column="valid_to"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMemberPermissionVo">
        select id, member_id, permission_id, is_granted, scope, valid_from, valid_to, delete_time, create_by, update_by, create_time, update_time from a_member_permission
    </sql>

    <select id="selectMemberPermissionList" parameterType="ai.showlab.system.domain.MemberPermission" resultMap="MemberPermissionResult">
        <include refid="selectMemberPermissionVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="permissionId != null "> and permission_id = #{permissionId}</if>
            <if test="isGranted != null "> and is_granted = #{isGranted}</if>
            <if test="scope != null  and scope != ''"> and scope like concat('%', #{scope}, '%')</if>
            <if test="validFrom != null "> and valid_from &gt;= #{validFrom}</if>
            <if test="validTo != null "> and valid_to &lt;= #{validTo}</if>
            <if test="params.beginDeleteTime != null and params.beginDeleteTime != '' and params.endDeleteTime != null and params.endDeleteTime != ''">
                and delete_time between CAST(#{params.beginDeleteTime} as timestamptz)
                and CAST(#{params.endDeleteTime} as timestamptz)
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between CAST(#{params.beginCreateTime} as timestamptz)
                and CAST(#{params.endCreateTime} as timestamptz)
            </if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between CAST(#{params.beginUpdateTime} as timestamptz)
                and CAST(#{params.endUpdateTime} as timestamptz)
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
            order by update_time desc
    </select>
    
    <select id="selectMemberPermissionById" parameterType="Long" resultMap="MemberPermissionResult">
        <include refid="selectMemberPermissionVo"/>
        where id = #{id}
    </select>

    <insert id="insertMemberPermission" parameterType="ai.showlab.system.domain.MemberPermission">
        insert into a_member_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="permissionId != null">permission_id,</if>
            <if test="isGranted != null">is_granted,</if>
            <if test="scope != null">scope,</if>
            <if test="validFrom != null">valid_from,</if>
            <if test="validTo != null">valid_to,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="permissionId != null">#{permissionId},</if>
            <if test="isGranted != null">#{isGranted},</if>
            <if test="scope != null">#{scope},</if>
            <if test="validFrom != null">#{validFrom},</if>
            <if test="validTo != null">#{validTo},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMemberPermission" parameterType="ai.showlab.system.domain.MemberPermission">
        update a_member_permission
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="permissionId != null">permission_id = #{permissionId},</if>
            <if test="isGranted != null">is_granted = #{isGranted},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="validFrom != null">valid_from = #{validFrom},</if>
            <if test="validTo != null">valid_to = #{validTo},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberPermissionById" parameterType="Long">
        delete from a_member_permission where id = #{id}
    </delete>

    <delete id="deleteMemberPermissionByIds" parameterType="String">
        delete from a_member_permission where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>