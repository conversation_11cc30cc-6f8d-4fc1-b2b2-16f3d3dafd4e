package ai.showlab.system.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import ai.showlab.system.domain.BillingBalance;

/**
 * 会员余额Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Repository
public interface BillingBalanceMapper {
    /**
     * 查询会员余额
     * 
     * @param id 会员余额主键
     * @return 会员余额
     */
    BillingBalance selectBillingBalanceById(Long id);

    /**
     * 查询会员余额列表
     * 
     * @param billingBalance 会员余额
     * @return 会员余额集合
     */
    List<BillingBalance> selectBillingBalanceList(BillingBalance billingBalance);

    /**
     * 新增会员余额
     * 
     * @param billingBalance 会员余额
     * @return 新增行数
     */
    int insertBillingBalance(BillingBalance billingBalance);

    /**
     * 修改会员余额
     * 
     * @param billingBalance 会员余额
     * @return 修改行数
     */
    int updateBillingBalance(BillingBalance billingBalance);

    /**
     * 删除会员余额
     * 
     * @param id 会员余额主键
     * @return 删除行数
     */
    int deleteBillingBalanceById(Long id);

    /**
     * 批量删除会员余额
     * 
     * @param ids 需要删除的数据主键集合
     * @return 删除行数
     */
    int deleteBillingBalanceByIds(Long[] ids);
}
