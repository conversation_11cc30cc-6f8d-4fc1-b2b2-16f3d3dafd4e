package ai.showlab.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐订单对象 a_billing_order
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BillingOrder extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Excel(name = "主键ID")
    private Long id;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 会员 */
    @Excel(name = "会员")
    private Long memberId;

    /** 套餐 */
    @Excel(name = "套餐")
    private Long packageId;

    /** 套餐信息快照 */
    private String packageInfoSnapshot;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal amount;

    /** 货币 */
    @Excel(name = "货币")
    private Long currencyId;

    /** 订单状态 */
    @Excel(name = "订单状态")
    private Integer status;

    /** 支付网关 */
    @Excel(name = "支付网关")
    private Long paymentGatewayId;

    /** 交易流水号 */
    @Excel(name = "交易流水号")
    private String gatewayTransactionId;

    /** 支付完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "支付完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paidTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("memberId", getMemberId())
            .append("packageId", getPackageId())
            .append("packageInfoSnapshot", getPackageInfoSnapshot())
            .append("amount", getAmount())
            .append("currencyId", getCurrencyId())
            .append("status", getStatus())
            .append("paymentGatewayId", getPaymentGatewayId())
            .append("gatewayTransactionId", getGatewayTransactionId())
            .append("paidTime", getPaidTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
