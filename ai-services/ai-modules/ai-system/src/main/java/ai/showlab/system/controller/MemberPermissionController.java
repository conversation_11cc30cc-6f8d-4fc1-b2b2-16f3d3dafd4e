package ai.showlab.system.controller;

import ai.showlab.system.domain.MemberPermission;
import ai.showlab.system.service.IMemberPermissionService;
import ai.showlab.system.utils.CommUtils;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员权限 Controller
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/member/permissions")
public class MemberPermissionController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(MemberPermissionController.class);

    @Autowired
    private IMemberPermissionService memberPermissionService;

    /**
     * 查询会员权限列表
     */
    @RequiresPermissions("system:memberPermission:list")
    @GetMapping("/list")
    public TableDataInfo list(MemberPermission memberPermission) {
        try {
            startPage();
            List<MemberPermission> list = memberPermissionService.selectMemberPermissionList(memberPermission);
            return getDataTable(list);
        } catch (ServiceException e) {
            return CommUtils.getDataTable(e.getMessage());
        } catch (Exception e) {
            log.error("查询会员权限列表失败", e);
            return CommUtils.getDataTable("查询会员权限列表失败");
        }
    }

    /**
     * 导出会员权限列表
     */
    @RequiresPermissions("system:memberPermission:export")
    @Log(title = "导出会员权限列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberPermission memberPermission) {
        try {
            List<MemberPermission> list = memberPermissionService.selectMemberPermissionList(memberPermission);
            ExcelUtil<MemberPermission> util = new ExcelUtil<>(MemberPermission.class);
            util.exportExcel(response, list, "会员权限数据");
        } catch (Exception e) {
            log.error("导出会员权限列表失败", e);
        }
    }

    /**
     * 会员权限详情
     */
    @RequiresPermissions("system:memberPermission:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        try {
            return memberPermissionService.selectMemberPermissionById(id);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取会员权限详情失败", e);
            return AjaxResult.error("获取会员权限详细信息失败");
        }
    }

    /**
     * 新增会员权限
     */
    @RequiresPermissions("system:memberPermission:add")
    @Log(title = "会员权限", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberPermission memberPermission) {
        try {
            return toAjax(memberPermissionService.insertMemberPermission(memberPermission));
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增会员权限失败", e);
            return AjaxResult.error("新增会员权限失败");
        }
    }

    /**
     * 修改会员权限
     */
    @RequiresPermissions("system:memberPermission:edit")
    @Log(title = "会员权限", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberPermission memberPermission) {
        try {
            return toAjax(memberPermissionService.updateMemberPermission(memberPermission));
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改会员权限失败", e);
            return AjaxResult.error("修改会员权限失败");
        }
    }

    /**
     * 删除会员权限
     */
    @RequiresPermissions("system:memberPermission:remove")
    @Log(title = "会员权限", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        try {
            return toAjax(memberPermissionService.deleteMemberPermissionByIds(ids));
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除会员权限失败", e);
            return AjaxResult.error("删除会员权限失败");
        }
    }
}
