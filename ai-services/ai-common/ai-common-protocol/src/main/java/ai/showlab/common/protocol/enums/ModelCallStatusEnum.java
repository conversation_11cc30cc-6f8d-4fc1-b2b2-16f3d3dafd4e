package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型调用状态枚举
 * 对应数据字典 model_call_status
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModelCallStatusEnum {
    
    /** 成功 */
    SUCCESS(1, "成功"),
    
    /** 失败 */
    FAILED(2, "失败"),
    
    /** 处理中 */
    PENDING(3, "处理中"),
    
    /** 超时 */
    TIMEOUT(4, "超时");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ModelCallStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelCallStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 检查状态是否为成功状态
     * 
     * @param code 状态码
     * @return 是否成功
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }
    
    /**
     * 检查状态是否为失败状态（包括失败和超时）
     * 
     * @param code 状态码
     * @return 是否失败
     */
    public static boolean isFailed(Integer code) {
        return FAILED.getCode().equals(code) || TIMEOUT.getCode().equals(code);
    }
    
    /**
     * 检查状态是否为处理中
     * 
     * @param code 状态码
     * @return 是否处理中
     */
    public static boolean isPending(Integer code) {
        return PENDING.getCode().equals(code);
    }
    
    /**
     * 检查状态是否为超时
     * 
     * @param code 状态码
     * @return 是否超时
     */
    public static boolean isTimeout(Integer code) {
        return TIMEOUT.getCode().equals(code);
    }
    
    /**
     * 检查当前枚举是否为成功状态
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 检查当前枚举是否为失败状态（包括失败和超时）
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return this == FAILED || this == TIMEOUT;
    }
    
    /**
     * 检查当前枚举是否为处理中
     * 
     * @return 是否处理中
     */
    public boolean isPending() {
        return this == PENDING;
    }
    
    /**
     * 检查当前枚举是否为超时
     * 
     * @return 是否超时
     */
    public boolean isTimeout() {
        return this == TIMEOUT;
    }
}
