# 自定义缓存注解 CustomCache

## 简介

`CustomCache` 是一个自定义注解，用于扩展 Spring 的 `@Cacheable` 功能，增加了设置缓存过期时间（TTL）的能力。

## 功能特点

- 支持所有 `@Cacheable` 的基本功能
- 可以为缓存设置过期时间（TTL）
- 支持 SpEL 表达式定义缓存键
- 支持条件缓存和排除条件
- 可以在没有 Redis 的环境下优雅降级（仅使用本地缓存）

## 使用方法

### 基本用法

```java
@CustomCache(value = "cacheName", key = "#param", ttl = 3600)
public Object methodName(String param) {
    // 方法实现
}
```

### 参数说明

- `value`: 缓存名称，必填
- `key`: 缓存键表达式，支持 SpEL，默认使用方法名和参数值
- `ttl`: 过期时间，单位为秒，默认为 3600 秒（1小时）
- `condition`: 缓存条件，满足条件时才缓存，支持 SpEL
- `unless`: 排除条件，满足条件时不缓存，支持 SpEL

### 示例

```java
// 基本用法，缓存1小时
@CustomCache(value = "documents", key = "#code + ':' + #langId", ttl = 3600)
public Document getDocument(String code, Long langId) {
    return documentRepository.findByCodeAndLangId(code, langId);
}

// 条件缓存，只缓存非空结果
@CustomCache(value = "users", key = "#userId", ttl = 1800, unless = "#result == null")
public User getUser(Long userId) {
    return userRepository.findById(userId).orElse(null);
}

// 条件缓存，只缓存特定条件的结果
@CustomCache(value = "products", key = "#productId", ttl = 7200, condition = "#productId > 0")
public Product getProduct(Long productId) {
    return productRepository.findById(productId).orElse(null);
}
```

## 配置说明

### Redis 配置

为了使用 TTL 功能，需要配置 Redis。在项目中添加以下配置类：

```java
@Configuration
@EnableCaching
public class RedisConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        
        // 使用GenericJackson2JsonRedisSerializer来序列化和反序列化redis的value值
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
}
```

### 无 Redis 环境

如果没有 Redis 环境，注解仍然可以工作，但不会设置过期时间。缓存将使用 Spring 的默认缓存实现（如 ConcurrentMapCache）。

## 实现原理

`CustomCache` 注解通过 AOP 实现，使用 `CustomCacheAspect` 拦截带有该注解的方法调用。当方法被调用时，切面会：

1. 尝试从缓存中获取结果
2. 如果缓存未命中，执行原方法并缓存结果
3. 如果 Redis 可用，为缓存设置指定的过期时间

## 注意事项

1. 需要确保项目中配置了 Redis 缓存（如果需要 TTL 功能）
2. 过期时间单位为秒
3. 缓存键支持 SpEL 表达式，可以使用 `#参数名` 引用方法参数
4. 在 `unless` 表达式中可以使用 `#result` 引用方法返回值
5. 如果没有 Redis 环境，注解仍然可以工作，但不会设置过期时间 