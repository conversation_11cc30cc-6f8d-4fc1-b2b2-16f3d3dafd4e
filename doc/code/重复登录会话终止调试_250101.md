# 重复登录会话终止调试

## 问题描述

重复登录时没有正确更新 `a_member_session` 的 `is_active` 字段和缓存。

## 可能的原因

1. **缓存注解冲突**：`@CacheEvict` 和 `refreshSessionCache` 同时使用可能导致冲突
2. **事务问题**：数据库更新和缓存更新不在同一事务中
3. **SQL执行问题**：`deactivateMemberSession` 方法可能没有正确执行

## 修复内容

### 1. 移除缓存注解冲突

```java
// 修改前
@CacheEvict(value = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY, key = "#memberId")
public void terminateSession(Long memberId, Long sessionId) {
    // ...
    refreshSessionCache(memberId); // 冲突！
}

// 修改后
public void terminateSession(Long memberId, Long sessionId) {
    // ...
    refreshSessionCache(memberId); // 统一使用主动刷新
}
```

### 2. 增强日志记录

添加详细的日志来跟踪：
- 会话终止的开始和结束
- 数据库更新的影响行数
- 缓存清除和刷新的结果

## 调试步骤

### 1. 清理环境

```sql
-- 清理测试用户的所有会话
DELETE FROM a_member_session WHERE member_id = 4;

-- 确认清理结果
SELECT COUNT(*) FROM a_member_session WHERE member_id = 4;
```

```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::4"
redis-cli keys "bff:member:session:token::*" | xargs redis-cli del
```

### 2. 设置日志级别

```yaml
# 在 application.yml 中设置
logging:
  level:
    ai.showlab.bff.service.v1.member.impl.MemberSessionServiceImpl: INFO
    ai.showlab.bff.service.v1.member.impl.DuplicateLoginServiceImpl: INFO
    ai.showlab.bff.service.v1.member.impl.MemberAuthServiceImpl: INFO
```

### 3. 第一次登录

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**期望日志**：
```
用户 4 重复登录检查：当前活跃会话数量 0
用户 4 首次登录，允许
为用户 4 创建会话记录成功
从数据库查询到用户 4 的最新活跃会话数量: 1
成功更新用户 4 的会话列表缓存，会话数量: 1
为用户 4 刷新会话列表缓存成功
```

**验证数据库**：
```sql
SELECT id, member_id, ip_address, is_active, login_time 
FROM a_member_session 
WHERE member_id = 4;
```
期望：1条 `is_active=true` 的记录

**验证缓存**：
```bash
redis-cli get "bff:member:sessions:id::4"
```
期望：包含1个会话的JSON数组

### 4. 第二次登录（重复登录）

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**期望日志**：
```
用户 4 重复登录检查：当前活跃会话数量 1
活跃会话 - ID: 123, IP: 127.0.0.1, 登录时间: 2024-08-01T10:00:00, Token前缀: abcd1234
用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
用户 4 需要踢掉 1 个旧会话，保留 0 个会话
准备踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T10:00:00, Token前缀: abcd1234
开始终止用户 4 的会话，会话ID: 123, Token前缀: abcd1234
数据库更新结果：用户 4 的会话 123 更新了 1 行记录
已清除Token缓存: abcd1234
从数据库查询到用户 4 的最新活跃会话数量: 0
成功更新用户 4 的会话列表缓存，会话数量: 0
成功终止用户 4 的会话 123
成功踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T10:00:00
用户 4 登录时踢掉了 1 个旧会话
踢掉旧会话后为用户 4 刷新会话列表缓存
为用户 4 创建会话记录成功
从数据库查询到用户 4 的最新活跃会话数量: 1
成功更新用户 4 的会话列表缓存，会话数量: 1
为用户 4 刷新会话列表缓存成功
```

**验证数据库**：
```sql
SELECT id, member_id, ip_address, is_active, login_time, create_time 
FROM a_member_session 
WHERE member_id = 4 
ORDER BY create_time DESC;
```
期望：2条记录，1条 `is_active=false`（被踢掉），1条 `is_active=true`（新登录）

**验证缓存**：
```bash
redis-cli get "bff:member:sessions:id::4"
```
期望：只包含1个活跃会话的JSON数组

### 5. 实时监控

#### 监控数据库变化

```bash
# 创建监控脚本
cat > monitor_db_changes.sh << 'EOF'
#!/bin/bash

echo "开始监控数据库会话变化..."

while true; do
    echo "=== $(date) ==="
    
    # 查询所有会话
    echo "所有会话:"
    psql -d your_db -c "
    SELECT id, member_id, 
           SUBSTRING(token, 1, 8) as token_prefix,
           ip_address, is_active, 
           login_time, create_time 
    FROM a_member_session 
    WHERE member_id = 4 
    ORDER BY create_time DESC;
    "
    
    # 查询活跃会话数量
    echo "活跃会话数量:"
    psql -d your_db -t -c "
    SELECT COUNT(*) 
    FROM a_member_session 
    WHERE member_id = 4 AND is_active = true;
    " | tr -d ' '
    
    echo "---"
    sleep 3
done
EOF

chmod +x monitor_db_changes.sh
./monitor_db_changes.sh
```

#### 监控Redis缓存变化

```bash
# 创建监控脚本
cat > monitor_cache_changes.sh << 'EOF'
#!/bin/bash

echo "开始监控Redis缓存变化..."

redis-cli monitor | grep "bff:member:sessions:id::4" | while read line; do
    echo "[$(date)] $line"
    
    # 获取当前缓存内容
    cache_content=$(redis-cli get "bff:member:sessions:id::4")
    if [ "$cache_content" != "" ] && [ "$cache_content" != "(nil)" ]; then
        session_count=$(echo "$cache_content" | jq '. | length' 2>/dev/null || echo "解析失败")
        echo "当前缓存会话数量: $session_count"
    else
        echo "缓存为空"
    fi
    echo "---"
done
EOF

chmod +x monitor_cache_changes.sh
./monitor_cache_changes.sh
```

### 6. 手动验证SQL

如果怀疑SQL执行有问题，可以手动执行：

```sql
-- 1. 创建测试会话
INSERT INTO a_member_session (member_id, token, device_type, device_info, ip_address, login_time, expire_time, is_active, create_time, update_time)
VALUES (4, 'test_token_123', 1, 'Test Device', '127.0.0.1', NOW(), NOW() + INTERVAL '30 days', true, NOW(), NOW());

-- 2. 查询会话
SELECT id, member_id, token, is_active FROM a_member_session WHERE member_id = 4;

-- 3. 手动执行deactivate
UPDATE a_member_session 
SET is_active = false, update_time = now() 
WHERE token = 'test_token_123' AND delete_time IS NULL;

-- 4. 验证结果
SELECT id, member_id, token, is_active FROM a_member_session WHERE member_id = 4;

-- 5. 清理测试数据
DELETE FROM a_member_session WHERE token = 'test_token_123';
```

## 故障排除

### 1. 如果数据库更新行数为0

**可能原因**：
- Token不存在
- 会话已经被删除（delete_time不为null）
- 会话已经是inactive状态

**排查方法**：
```sql
-- 查找Token对应的会话
SELECT id, member_id, token, is_active, delete_time 
FROM a_member_session 
WHERE token = 'your_token_here';
```

### 2. 如果缓存没有更新

**可能原因**：
- Redis连接问题
- 缓存key不正确
- 序列化问题

**排查方法**：
```bash
# 检查Redis连接
redis-cli ping

# 检查缓存key
redis-cli keys "bff:member:sessions:id::*"

# 手动设置测试缓存
redis-cli set "test_key" "test_value"
redis-cli get "test_key"
```

### 3. 如果事务回滚

**可能原因**：
- 数据库约束违反
- 其他异常导致事务回滚

**排查方法**：
- 检查应用日志中的异常信息
- 检查数据库日志
- 添加更多的try-catch块

## 预期修复效果

修复后，重复登录应该：

1. **正确检测重复登录**：基于最新的活跃会话数据
2. **正确终止旧会话**：将 `is_active` 设置为 `false`
3. **正确更新缓存**：反映最新的会话状态
4. **详细的日志记录**：便于调试和监控

## 性能监控

### 关键指标

1. **数据库更新成功率**：`deactivateMemberSession` 的影响行数
2. **缓存刷新成功率**：`refreshSessionCache` 的成功次数
3. **重复登录检测准确性**：检测到的重复登录次数vs实际重复登录次数

### 监控查询

```sql
-- 统计会话状态分布
SELECT is_active, COUNT(*) 
FROM a_member_session 
WHERE member_id = 4 
GROUP BY is_active;

-- 查看最近的会话变更
SELECT id, member_id, is_active, login_time, update_time 
FROM a_member_session 
WHERE member_id = 4 
ORDER BY update_time DESC 
LIMIT 10;
```
