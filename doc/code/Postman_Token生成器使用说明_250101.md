# Postman Token生成器使用说明

## 概述

为了方便在Postman中测试API，我们提供了两个工具类：

1. **PostmanTokenGenerator** - 专门用于生成Postman Token（推荐）
2. **TestBffLogin** - 完整的登录测试工具（参考ruoyi-cloud风格）

## 快速使用 PostmanTokenGenerator

### 1. 运行生成器

```java
// 直接运行main方法
PostmanTokenGenerator.main(new String[]{});

// 或者调用生成方法
PostmanTokenGenerator.generateToken("13800138000", "aaBB@888");
```

### 2. 输出示例

```
=== Postman Token 生成器 ===
正在为用户 13800138000 生成Token...
✅ 登录成功!

==================================================
📋 Postman Authorization 配置
==================================================

🔧 方法1: 使用 Bearer Token
1. 在Postman中选择 Authorization 标签
2. Type 选择: Bearer Token
3. Token 填入: eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI0IiwibWVtYmVySWQiOjQsInVzZXJuYW1lIjoiMTM4MDAxMzgwMDAiLCJuaWNrbmFtZSI6IuaWsOeUqOaItyIsImF1dGhUeXBlIjoyLCJpYXQiOjE3MzU3MDY0NDAsImV4cCI6MTczODI5ODQ0MH0.xxx

🔧 方法2: 使用 Headers
1. 在Postman中选择 Headers 标签
2. 添加新的Header:
   Key: Authorization
   Value: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI0IiwibWVtYmVySWQiOjQsInVzZXJuYW1lIjoiMTM4MDAxMzgwMDAiLCJuaWNrbmFtZSI6IuaWsOeUqOaItyIsImF1dGhUeXBlIjoyLCJpYXQiOjE3MzU3MDY0NDAsImV4cCI6MTczODI5ODQ0MH0.xxx

📝 完整的Authorization值:
Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI0IiwibWVtYmVySWQiOjQsInVzZXJuYW1lIjoiMTM4MDAxMzgwMDAiLCJuaWNrbmFtZSI6IuaWsOeUqOaItyIsImF1dGhUeXBlIjoyLCJpYXQiOjE3MzU3MDY0NDAsImV4cCI6MTczODI5ODQ0MH0.xxx

⏰ Token信息:
   用户ID: 4
   用户名: 13800138000
   昵称: 新用户
   过期时间: 2025-01-31T10:00:40Z

==================================================

🔍 验证Token有效性...
✅ Token验证成功，可以正常使用!
👤 当前用户信息:
   ID: 4
   用户名: 13800138000
   昵称: 新用户
```

## Postman配置步骤

### 方法1：使用Bearer Token（推荐）

1. 在Postman中打开要测试的请求
2. 点击 **Authorization** 标签
3. **Type** 下拉选择 **Bearer Token**
4. 在 **Token** 字段中粘贴生成的Token（不包含"Bearer "前缀）
5. 发送请求

### 方法2：使用Headers

1. 在Postman中打开要测试的请求
2. 点击 **Headers** 标签
3. 添加新的Header：
   - **Key**: `Authorization`
   - **Value**: `Bearer eyJhbGciOiJIUzI1NiJ9...`（完整的值）
4. 发送请求

## 常用API测试

### 1. 获取用户信息

```
POST http://localhost:9901/ai-bff/api/v1/member/profile
Authorization: Bearer <your_token>
```

### 2. 登出

```
POST http://localhost:9901/ai-bff/api/v1/member/logout
Authorization: Bearer <your_token>
```

### 3. 其他需要认证的API

所有需要登录的API都可以使用相同的Authorization配置。

## 自定义用户

如果需要为其他用户生成Token，修改main方法：

```java
public static void main(String[] args) {
    // 方式1：修改默认用户
    generateToken("your_phone", "your_password");
    
    // 方式2：生成多个用户的Token
    generateToken("13800138000", "aaBB@888");
    generateToken("<EMAIL>", "aaBB@888");
    generateToken("testuser", "aaBB@888");
}
```

## 编程方式调用

```java
// 在其他代码中快速获取Authorization头
String authHeader = PostmanTokenGenerator.quickGenerate("13800138000", "aaBB@888");
if (authHeader != null) {
    // 使用authHeader进行API调用
    System.out.println("Authorization: " + authHeader);
}
```

## 故障排除

### 1. 登录失败

**可能原因**：
- 用户名/密码错误
- BFF服务未启动
- 网络连接问题

**解决方法**：
```bash
# 检查BFF服务是否启动
curl http://localhost:9901/ai-bff/actuator/health

# 检查用户是否存在
# 查看数据库中的用户信息
```

### 2. Token验证失败

**可能原因**：
- Token已过期
- Token格式错误
- 服务端配置问题

**解决方法**：
- 重新生成Token
- 检查Token是否完整
- 查看服务端日志

### 3. 服务地址错误

如果BFF服务不在默认地址，修改 `DEFAULT_BASE_URL`：

```java
private static final String DEFAULT_BASE_URL = "http://your-server:port/ai-bff";
```

## 高级用法

### 1. 批量生成Token

```java
public class BatchTokenGenerator {
    public static void main(String[] args) {
        String[] users = {
            "13800138000",
            "<EMAIL>", 
            "testuser"
        };
        
        for (String user : users) {
            System.out.println("\n=== 用户: " + user + " ===");
            PostmanTokenGenerator.generateToken(user, "aaBB@888");
        }
    }
}
```

### 2. 集成到测试脚本

```java
@Test
public void testApiWithToken() {
    // 生成Token
    String authHeader = PostmanTokenGenerator.quickGenerate("13800138000", "aaBB@888");
    assertNotNull(authHeader);
    
    // 使用Token调用API
    RestTemplate restTemplate = new RestTemplate();
    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", authHeader);
    
    HttpEntity<String> entity = new HttpEntity<>(headers);
    ResponseEntity<String> response = restTemplate.exchange(
        "http://localhost:9901/ai-bff/api/v1/member/profile",
        HttpMethod.POST,
        entity,
        String.class
    );
    
    assertEquals(HttpStatus.OK, response.getStatusCode());
}
```

## 与TestBffLogin的区别

| 特性 | PostmanTokenGenerator | TestBffLogin |
|------|----------------------|--------------|
| 主要用途 | 生成Postman Token | 完整登录测试 |
| 输出格式 | 格式化的配置说明 | 详细的测试日志 |
| 功能范围 | 专注Token生成 | 登录、登出、验证 |
| 使用复杂度 | 简单 | 复杂 |
| 参考风格 | 专用工具 | ruoyi-cloud风格 |

## 推荐使用场景

- **日常API测试**: 使用 `PostmanTokenGenerator`
- **完整功能测试**: 使用 `TestBffLogin`
- **自动化测试**: 使用 `PostmanTokenGenerator.quickGenerate()`
- **调试问题**: 使用 `TestBffLogin.fullTest()`

## 注意事项

1. **Token过期**: JWT Token有过期时间，需要定期重新生成
2. **安全性**: 这些工具仅用于开发测试，不要在生产环境使用
3. **网络环境**: 确保能够访问BFF服务
4. **用户权限**: 确保测试用户有足够的权限访问相关API
