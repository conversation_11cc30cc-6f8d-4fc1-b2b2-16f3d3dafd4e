# 重复登录功能测试说明

## 问题分析

在测试重复登录功能时发现：
1. 同一个浏览器重复登录时，登录成功
2. 在 `a_member_session` 表中创建了2条记录，`is_active` 都是 `true`
3. 日志中没有重复登录检测的提示

## 根本原因

**配置问题**：`check-same-device: false`（默认值）导致同设备重复登录不受限制。

当 `check-same-device=false` 时：
- 系统会过滤掉同设备（相同IP）的会话
- 同一个浏览器的重复登录被认为是"同设备"
- 过滤后的会话数量小于最大限制，因此允许登录

## 解决方案

### 方案1：启用同设备检查（推荐）

修改配置文件 `doc/nacos/ai-bff-dev.yml`：

```yaml
app:
  security:
    duplicate-login:
      strategy: KICK_OLD
      max-concurrent-sessions: 3
      check-same-device: true  # 改为 true
```

**效果**：同设备的重复登录也会受到限制

### 方案2：调整最大会话数

如果希望同设备不受限制，但限制总会话数：

```yaml
app:
  security:
    duplicate-login:
      strategy: KICK_OLD
      max-concurrent-sessions: 1  # 设置为1
      check-same-device: false
```

**效果**：只允许1个会话，无论是否同设备

## 测试步骤

### 1. 准备工作

1. **清理现有会话**：
   ```sql
   DELETE FROM a_member_session WHERE member_id = 4;
   ```

2. **确认配置**：
   - 检查 Nacos 配置中的重复登录设置
   - 重启服务使配置生效

### 2. 测试场景

#### 场景1：同设备重复登录测试

**配置**：`check-same-device: true`, `max-concurrent-sessions: 1`

1. **第一次登录**：
   ```bash
   curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
   -H "Content-Type: application/json" \
   -d '{"identifier":"13800138000","password":"aaBB@888"}'
   ```

2. **第二次登录**（同一IP）：
   ```bash
   curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
   -H "Content-Type: application/json" \
   -d '{"identifier":"13800138000","password":"aaBB@888"}'
   ```

**期望结果**：
- 第二次登录成功
- 第一个会话被踢掉（`is_active=false`）
- 日志显示重复登录检测和处理信息

#### 场景2：不同设备登录测试

**配置**：`check-same-device: true`, `max-concurrent-sessions: 2`

1. **设备1登录**（IP: *************）
2. **设备2登录**（IP: *************）
3. **设备3登录**（IP: *************）

**期望结果**：
- 前两次登录成功
- 第三次登录时踢掉最旧的会话

### 3. 验证方法

#### 检查数据库

```sql
-- 查看会话记录
SELECT id, member_id, ip_address, login_time, is_active, create_time 
FROM a_member_session 
WHERE member_id = 4 
ORDER BY create_time DESC;

-- 查看活跃会话数量
SELECT COUNT(*) as active_sessions 
FROM a_member_session 
WHERE member_id = 4 AND is_active = true;
```

#### 检查日志

关键日志信息：
```
用户 4 重复登录检查：当前活跃会话数量 1
用户 4 当前登录IP: 127.0.0.1, UserAgent: Mozilla/5.0...
用户 4 过滤同设备会话后：原始会话数 1, 过滤后会话数 0
用户 4 会话数量未超限：当前 0 < 最大 3, 允许登录
```

或者：
```
用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
用户 4 登录时踢掉了 1 个旧会话
踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T10:00:00
```

## 配置说明

### 参数详解

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `strategy` | 重复登录策略 | `KICK_OLD` |
| `max-concurrent-sessions` | 最大同时会话数 | `3` |
| `check-same-device` | 是否检查同设备 | `true` |

### 策略对比

| 策略 | 描述 | 适用场景 |
|------|------|----------|
| `ALLOW_MULTIPLE` | 允许无限制登录 | 开发测试环境 |
| `KICK_OLD` | 踢掉旧会话 | 一般业务场景 |
| `REJECT_NEW` | 拒绝新登录 | 高安全要求 |

## 故障排除

### 1. 日志中没有重复登录检测信息

**可能原因**：
- `IDuplicateLoginService` 没有被正确注入
- 配置文件没有生效
- 请求对象为空

**解决方法**：
- 检查服务是否正确启动
- 确认配置文件路径和格式
- 添加调试日志

### 2. 会话没有被踢掉

**可能原因**：
- 数据库事务没有提交
- 缓存没有清除
- Token黑名单没有生效

**解决方法**：
- 检查事务配置
- 手动清除缓存
- 验证Token验证逻辑

### 3. 同设备判断不准确

**当前限制**：只使用IP地址判断同设备

**改进方向**：
- 结合User-Agent
- 使用设备指纹
- 添加设备ID机制

## 监控建议

1. **会话数量监控**：监控用户的活跃会话数量
2. **重复登录频率**：统计重复登录的频率和模式
3. **异常登录检测**：检测异常的登录行为
4. **性能监控**：监控重复登录检查的性能影响
