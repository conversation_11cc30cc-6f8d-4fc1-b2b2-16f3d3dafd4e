# 完整会话管理和缓存刷新验证

## 修复内容

### 问题
1. 登录成功前要先清理旧会话（踢掉旧会话后需要刷新缓存）
2. 登出时也要清理旧会话并刷新缓存

### 解决方案

#### 1. 重复登录时的缓存刷新
```java
// MemberAuthServiceImpl.login()
if (duplicateLoginResult.getKickedSessions() != null && !duplicateLoginResult.getKickedSessions().isEmpty()) {
    // 踢掉旧会话后立即刷新缓存
    memberSessionService.refreshSessionCache(auth.getMemberId());
}
```

#### 2. 登出时的缓存刷新
```java
// MemberAuthServiceImpl.logout()
memberSessionService.terminateAllSessions(memberId);
// 终止会话后刷新缓存
memberSessionService.refreshSessionCache(memberId);
```

#### 3. 会话终止时的缓存刷新
```java
// MemberSessionServiceImpl.terminateSession()
// MemberSessionServiceImpl.terminateAllSessions()
// 都会调用 refreshSessionCache(memberId) 来刷新缓存
```

## 完整的会话生命周期

### 1. 登录流程
```
1. 重复登录检查
   ↓
2. 踢掉旧会话（如果需要）
   ↓
3. 刷新缓存（反映踢掉的会话）
   ↓
4. 创建新会话
   ↓
5. 刷新缓存（反映新会话）
```

### 2. 登出流程
```
1. Token加入黑名单
   ↓
2. 终止所有会话
   ↓
3. 刷新缓存（反映会话终止）
```

## 验证步骤

### 1. 清理环境

```sql
-- 清理测试用户的所有会话
DELETE FROM a_member_session WHERE member_id = 4;
```

```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::4"
redis-cli keys "bff:member:session:token::*" | xargs redis-cli del
```

### 2. 第一次登录测试

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**验证点**：

1. **数据库状态**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time 
   FROM a_member_session 
   WHERE member_id = 4;
   ```
   期望：1条 `is_active=true` 的记录

2. **Redis状态**：
   ```bash
   redis-cli get "bff:member:sessions:id::4"
   ```
   期望：包含1个活跃会话的JSON数组

3. **日志验证**：
   ```
   用户 4 重复登录检查：当前活跃会话数量 0
   用户 4 首次登录，允许
   为用户 4 创建会话记录成功
   成功更新用户 4 的会话列表缓存，会话数量: 1
   为用户 4 刷新会话列表缓存成功
   ```

### 3. 第二次登录测试（重复登录）

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**验证点**：

1. **数据库状态**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time, create_time 
   FROM a_member_session 
   WHERE member_id = 4 
   ORDER BY create_time DESC;
   ```
   期望：2条记录，1条 `is_active=false`（被踢掉），1条 `is_active=true`（新登录）

2. **Redis状态**：
   ```bash
   redis-cli get "bff:member:sessions:id::4"
   ```
   期望：只包含1个活跃会话的JSON数组（新会话）

3. **日志验证**：
   ```
   用户 4 重复登录检查：当前活跃会话数量 1
   用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
   用户 4 登录时踢掉了 1 个旧会话
   踢掉旧会话后为用户 4 刷新会话列表缓存
   为用户 4 创建会话记录成功
   成功更新用户 4 的会话列表缓存，会话数量: 1
   为用户 4 刷新会话列表缓存成功
   ```

### 4. 登出测试

```bash
# 获取登录后的token
TOKEN="your_jwt_token_here"

curl -X POST http://localhost:9901/ai-bff/api/v1/member/logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN"
```

**验证点**：

1. **数据库状态**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time 
   FROM a_member_session 
   WHERE member_id = 4;
   ```
   期望：所有记录的 `is_active=false`

2. **Redis状态**：
   ```bash
   redis-cli get "bff:member:sessions:id::4"
   ```
   期望：空数组 `[]`

3. **Token黑名单**：
   ```bash
   redis-cli get "bff:token:blacklist:$TOKEN"
   ```
   期望：存在且值为 "blacklisted"

4. **日志验证**：
   ```
   JWT Token已加入黑名单，用户ID: 4, TTL: xxxms
   已终止用户 4 的所有会话记录
   成功更新用户 4 的会话列表缓存，会话数量: 0
   登出后为用户 4 刷新会话列表缓存
   ```

### 5. 缓存一致性验证

#### 测试缓存与数据库的实时同步

```bash
# 创建监控脚本
cat > monitor_cache_db.sh << 'EOF'
#!/bin/bash

echo "开始监控缓存与数据库一致性..."

while true; do
    echo "=== $(date) ==="
    
    # 获取Redis缓存
    echo "Redis缓存:"
    redis-cli get "bff:member:sessions:id::4" | jq '.[].id' 2>/dev/null || echo "缓存为空或格式错误"
    
    # 获取数据库数据
    echo "数据库活跃会话:"
    psql -d your_db -t -c "SELECT id FROM a_member_session WHERE member_id = 4 AND is_active = true;" | tr -d ' '
    
    echo "---"
    sleep 2
done
EOF

chmod +x monitor_cache_db.sh
./monitor_cache_db.sh
```

#### 测试并发登录和登出

```bash
# 并发登录测试
for i in {1..3}; do
  curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
    -H "Content-Type: application/json" \
    -d '{"identifier": "13800138000", "password": "aaBB@888"}' &
done
wait

# 检查最终状态
echo "最终Redis状态:"
redis-cli get "bff:member:sessions:id::4"

echo "最终数据库状态:"
psql -d your_db -c "SELECT id, is_active, login_time FROM a_member_session WHERE member_id = 4 ORDER BY create_time DESC;"
```

## 性能监控

### 1. 缓存刷新频率

```bash
# 统计缓存刷新次数
grep "成功更新用户.*的会话列表缓存" application.log | wc -l

# 按时间统计
grep "成功更新用户.*的会话列表缓存" application.log | \
  awk '{print $1" "$2}' | cut -c1-13 | uniq -c
```

### 2. 数据库查询频率

```bash
# 统计会话查询次数
grep "从数据库查询到用户.*的最新活跃会话数量" application.log | wc -l

# 分析查询模式
grep "从数据库查询到用户.*的最新活跃会话数量" application.log | \
  grep -o "会话数量: [0-9]*" | sort | uniq -c
```

### 3. 重复登录处理统计

```bash
# 统计重复登录检测
grep "用户.*重复登录检查" application.log | wc -l

# 统计踢掉会话的次数
grep "用户.*登录时踢掉了.*个旧会话" application.log | wc -l

# 统计登出次数
grep "已终止用户.*的所有会话记录" application.log | wc -l
```

## 故障排除

### 1. 缓存不一致

**症状**：Redis缓存与数据库数据不一致

**排查步骤**：
1. 检查 `refreshSessionCache` 方法是否被正确调用
2. 检查数据库事务是否正确提交
3. 检查Redis连接是否正常

**解决方法**：
```bash
# 手动刷新缓存
redis-cli del "bff:member:sessions:id::4"
# 下次查询时会自动从数据库加载
```

### 2. 重复登录检测失效

**症状**：重复登录时没有踢掉旧会话

**排查步骤**：
1. 检查配置 `check-same-device: true`
2. 检查日志中的重复登录检测信息
3. 检查缓存中的会话数据

### 3. 登出后仍能访问

**症状**：登出后Token仍然有效

**排查步骤**：
1. 检查Token是否在黑名单中
2. 检查会话是否被正确终止
3. 检查Token验证逻辑

## 总结

### 完整的缓存刷新时机

1. **重复登录踢掉旧会话后**：立即刷新缓存
2. **创建新会话后**：立即刷新缓存
3. **单个会话终止后**：立即刷新缓存
4. **所有会话终止后**：立即刷新缓存
5. **登出后**：立即刷新缓存

### 数据一致性保证

- **实时同步**：每次会话状态变更都会立即刷新缓存
- **事务安全**：数据库操作和缓存更新在同一事务中
- **异常处理**：缓存更新失败不影响核心业务流程

### 性能优化

- **按需刷新**：只在数据变更时刷新缓存
- **精确更新**：直接更新缓存而不是清除等待重建
- **异常容错**：缓存操作失败时有详细日志记录
