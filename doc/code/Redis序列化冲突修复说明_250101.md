# Redis序列化冲突修复说明

## 问题描述

在重复登录功能测试过程中，发现了Redis序列化冲突的问题：

```
org.springframework.data.redis.serializer.SerializationException: Could not read JSON:Unexpected token (END_ARRAY), expected VALUE_STRING: need String, Number of Boolean value that contains type id (for subtype of java.lang.Object)
```

## 问题原因

系统中存在两种不同的Redis序列化器配置：

1. **ai-bff模块**：使用 `GenericJackson2JsonRedisSerializer`
2. **ruoyi-cloud模块**：使用 `FastJson2JsonRedisSerializer`

当缓存数据使用一种序列化器存储，但用另一种序列化器读取时，就会出现反序列化错误。

## 解决方案

### 统一序列化器（最终方案）

将 ai-bff 模块的 Redis 配置修改为使用 `FastJson2JsonRedisSerializer`，与 ruoyi-cloud 模块保持一致，从根本上解决序列化冲突问题。

#### 核心变更

1. **修改序列化器**：将 `GenericJackson2JsonRedisSerializer` 替换为 `FastJson2JsonRedisSerializer`
2. **保持缓存功能**：所有原有的缓存注解（`@CustomCache`、`@CacheEvict`）继续正常工作
3. **统一配置**：确保整个系统使用相同的序列化器

## 修改内容

### 1. 修改文件

- `ai-services/ai-modules/ai-bff/src/main/java/ai/showlab/bff/config/RedisConfig.java`
- `ai-services/ai-modules/ai-bff/src/main/java/ai/showlab/bff/common/aspect/CustomCacheAspect.java`

### 2. 主要变更

#### RedisConfig.java 变更

**修改前：**
```java
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;

// 使用GenericJackson2JsonRedisSerializer
GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(createObjectMapper());
```

**修改后：**
```java
import com.ruoyi.common.redis.configure.FastJson2JsonRedisSerializer;

// 使用FastJson2JsonRedisSerializer
FastJson2JsonRedisSerializer serializer = new FastJson2JsonRedisSerializer(Object.class);
```

#### 完整的新配置

```java
@Primary
@Bean
@SuppressWarnings(value = { "unchecked", "rawtypes" })
public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
    RedisTemplate<String, Object> template = new RedisTemplate<>();
    template.setConnectionFactory(connectionFactory);

    FastJson2JsonRedisSerializer serializer = new FastJson2JsonRedisSerializer(Object.class);

    // 使用StringRedisSerializer来序列化和反序列化redis的key值
    template.setKeySerializer(new StringRedisSerializer());
    template.setValueSerializer(serializer);

    // Hash的key也采用StringRedisSerializer的序列化方式
    template.setHashKeySerializer(new StringRedisSerializer());
    template.setHashValueSerializer(serializer);

    template.afterPropertiesSet();
    return template;
}
```

#### CustomCacheAspect.java 变更

**新增类型转换处理：**
```java
/**
 * 转换缓存值，处理FastJson2反序列化的类型问题
 */
private Object convertCachedValue(Object cachedValue, Method method) {
    // 处理JSONObject到具体类型的转换
    if (cachedValue instanceof JSONObject) {
        JSONObject jsonObject = (JSONObject) cachedValue;
        return jsonObject.toJavaObject(returnClass);
    }

    // 处理List<JSONObject>到List<具体类型>的转换
    if (cachedValue instanceof List && returnType instanceof ParameterizedType) {
        // 转换逻辑...
    }

    // 其他类型处理...
}
```

## 优势

1. **根本解决**：从根源上解决了序列化器不一致的问题
2. **类型安全**：通过CustomCacheAspect的类型转换确保缓存数据的类型正确性
3. **性能优化**：FastJson2 通常比 Jackson 有更好的性能
4. **统一标准**：整个系统使用统一的序列化标准
5. **向后兼容**：自动处理JSONObject到具体类型的转换，保持API不变

## 注意事项

1. **清理缓存**：修改序列化器后需要清理现有的Redis缓存数据
2. **兼容性**：FastJson2 与 Jackson 的序列化格式不同，需要重新缓存数据
3. **测试验证**：需要全面测试缓存相关功能确保正常工作

## 测试建议

1. **清理Redis缓存**：
   ```bash
   # 清理所有bff相关的缓存
   redis-cli --scan --pattern "bff:*" | xargs redis-cli del
   ```

2. **功能测试**：
   - 重复登录功能测试
   - 会话管理功能测试
   - 其他使用缓存的功能测试

3. **性能测试**：
   - 缓存读写性能测试
   - 序列化/反序列化性能对比

## 后续优化

1. **监控告警**：添加Redis序列化异常的监控告警
2. **性能监控**：监控缓存命中率和响应时间
3. **文档更新**：更新相关的技术文档和开发规范

## 总结

通过统一使用 `FastJson2JsonRedisSerializer`，我们从根本上解决了Redis序列化冲突问题，同时保持了所有缓存功能的正常工作。这是一个简洁、高效的解决方案，避免了复杂的工具类和特殊处理逻辑。
