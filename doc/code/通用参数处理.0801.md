# 通用参数处理框架

## 概述

本框架通过 AOP 技术实现了接口参数的统一处理，主要功能包括：

1. 提取请求中的常用参数（如 id、memberId、type等）到通用参数对象中
2. 校验必要参数（如 apiVersion、sourceType）
3. 将参数转换为业务对象，并进行参数校验
4. 将处理后的参数传递到具体的接口方法中

## 核心组件

### 1. RequestParam 通用参数对象

包含常用参数（如 id、memberId、type）和扩展参数（extraParams），以及业务参数对象（bizParam）。

### 2. ApiParamValidate 注解

用于标记需要进行参数处理的接口方法，可配置是否校验必要参数、是否转换为业务对象等。

### 3. ApiParamAspect 切面类

实现了参数处理的核心逻辑，包括参数提取、校验和转换。

### 4. ParamValidateUtil 参数校验工具类

提供了基于 Jakarta Validation API 的参数校验功能。

## 使用方法

### 1. 定义业务参数对象

```java
@Data
public class MemberRegisterParam {
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    // 其他字段和校验注解
}
```

### 2. 在控制器方法上添加注解

```java
@ApiParamValidate(convertToBizParam = true, bizParamClass = MemberRegisterParam.class)
@PostMapping("/register")
public ResponseEntity<RestResult> register(RequestParam<MemberRegisterParam> requestParams) {
    // 获取业务参数
    MemberRegisterParam param = requestParams.getBizParam();
    
    // 校验参数
    ParamValidateUtil.validate(param);
    
    // 业务处理
    // ...
}
```

### 3. 访问通用参数

```java
// 获取常用参数
Long id = requestParams.getId();
Long memberId = requestParams.getMemberId();

// 获取扩展参数
Map<String, Object> extraParams = requestParams.getExtraParams();
String customParam = (String) extraParams.get("customParam");
```

## 优点

1. **统一处理**：统一处理请求参数，减少重复代码
2. **参数校验**：自动校验必要参数和业务参数
3. **类型转换**：自动将参数转换为业务对象
4. **灵活配置**：通过注解配置参数处理行为
5. **代码解耦**：将参数处理逻辑与业务逻辑分离

## 注意事项

1. 必须在控制器方法参数中添加 `RequestParam<T>` 类型的参数才能接收处理后的参数
2. 业务参数对象应当使用 Jakarta Validation API 的注解进行校验
3. 参数校验失败会抛出 ApiParamException 异常，需要全局异常处理器处理 