# 重复登录配置修复和测试

## 问题分析

从日志可以看出问题所在：

```
用户 4 重复登录检查：当前活跃会话数量 1
用户 4 会话数量未超限：当前 1 < 最大 3, 允许登录
```

**根本原因**：配置的 `max-concurrent-sessions: 3`，而当前只有1个会话，远小于3，所以不会触发重复登录处理。

## 解决方案

### 1. 修改配置

将 `max-concurrent-sessions` 从 3 改为 1：

```yaml
app:
  security:
    duplicate-login:
      strategy: KICK_OLD
      max-concurrent-sessions: 1  # 改为1来测试重复登录
      check-same-device: true
```

### 2. 重启服务

配置修改后需要重启服务使配置生效。

## 测试步骤

### 1. 清理环境

```sql
-- 清理测试用户的所有会话
DELETE FROM a_member_session WHERE member_id = 4;

-- 确认清理结果
SELECT COUNT(*) FROM a_member_session WHERE member_id = 4;
```

```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::4"
redis-cli keys "bff:member:session:token::*" | xargs redis-cli del
```

### 2. 重启服务

确保新的配置生效：
- `max-concurrent-sessions: 1`
- `check-same-device: true`
- `strategy: KICK_OLD`

### 3. 第一次登录测试

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**期望日志**：
```
用户 4 重复登录检查：当前活跃会话数量 0
用户 4 首次登录，允许
为用户 4 创建会话记录成功
成功更新用户 4 的会话列表缓存，会话数量: 1
为用户 4 刷新会话列表缓存成功
用户登录成功，会员ID: 4, 标识: 13800138000, 认证类型: 手机, IP: 127.0.0.1
```

**验证数据库**：
```sql
SELECT id, member_id, ip_address, is_active, login_time 
FROM a_member_session 
WHERE member_id = 4;
```
期望：1条 `is_active=true` 的记录

### 4. 第二次登录测试（重复登录）

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**期望日志**：
```
用户 4 重复登录检查：当前活跃会话数量 1
活跃会话 - ID: 123, IP: 127.0.0.1, 登录时间: 2024-08-01T13:14:00
用户 4 当前登录IP: 127.0.0.1, UserAgent: PostmanRuntime/7.29.2
用户 4 过滤同设备会话后：原始会话数 1, 过滤后会话数 1  # 因为check-same-device=true
用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
用户 4 需要踢掉 1 个旧会话，保留 0 个会话
准备踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T13:14:00, Token前缀: abcd1234
开始终止用户 4 的会话，会话ID: 123, Token前缀: abcd1234
数据库更新结果：用户 4 的会话 123 更新了 1 行记录
已清除Token缓存: abcd1234
从数据库查询到用户 4 的最新活跃会话数量: 0
成功更新用户 4 的会话列表缓存，会话数量: 0
成功终止用户 4 的会话 123
成功踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T13:14:00
用户 4 登录时踢掉了 1 个旧会话
踢掉旧会话后为用户 4 刷新会话列表缓存
为用户 4 创建会话记录成功
成功更新用户 4 的会话列表缓存，会话数量: 1
为用户 4 刷新会话列表缓存成功
用户登录成功，会员ID: 4, 标识: 13800138000, 认证类型: 手机, IP: 127.0.0.1
```

**验证数据库**：
```sql
SELECT id, member_id, ip_address, is_active, login_time, create_time 
FROM a_member_session 
WHERE member_id = 4 
ORDER BY create_time DESC;
```
期望：2条记录，1条 `is_active=false`（被踢掉），1条 `is_active=true`（新登录）

**验证缓存**：
```bash
redis-cli get "bff:member:sessions:id::4"
```
期望：只包含1个活跃会话的JSON数组

## 配置说明

### 不同场景的配置建议

#### 1. 测试环境（严格限制）
```yaml
app:
  security:
    duplicate-login:
      strategy: KICK_OLD
      max-concurrent-sessions: 1
      check-same-device: true
```
**效果**：任何重复登录都会踢掉旧会话

#### 2. 生产环境（允许多设备）
```yaml
app:
  security:
    duplicate-login:
      strategy: KICK_OLD
      max-concurrent-sessions: 3
      check-same-device: false
```
**效果**：允许3个不同设备同时登录，同设备不限制

#### 3. 高安全环境（拒绝重复登录）
```yaml
app:
  security:
    duplicate-login:
      strategy: REJECT_NEW
      max-concurrent-sessions: 1
      check-same-device: true
```
**效果**：拒绝任何重复登录

#### 4. 开放环境（无限制）
```yaml
app:
  security:
    duplicate-login:
      strategy: ALLOW_MULTIPLE
      max-concurrent-sessions: 999
      check-same-device: false
```
**效果**：不限制重复登录

## 验证配置是否生效

### 1. 检查配置加载

在应用启动日志中查找：
```
Loading configuration from: ai-bff-dev.yml
```

### 2. 添加配置验证日志

可以在 `DuplicateLoginServiceImpl` 中添加配置打印：

```java
@PostConstruct
public void logConfiguration() {
    log.info("重复登录配置 - 策略: {}, 最大会话数: {}, 检查同设备: {}", 
            duplicateLoginStrategy, maxConcurrentSessions, checkSameDevice);
}
```

### 3. 通过接口验证

可以创建一个配置查询接口：

```java
@GetMapping("/config/duplicate-login")
public ResponseEntity<Map<String, Object>> getDuplicateLoginConfig() {
    Map<String, Object> config = new HashMap<>();
    config.put("strategy", duplicateLoginStrategy);
    config.put("maxConcurrentSessions", maxConcurrentSessions);
    config.put("checkSameDevice", checkSameDevice);
    return ResponseEntity.ok(config);
}
```

## 故障排除

### 1. 配置没有生效

**可能原因**：
- 配置文件路径错误
- Nacos配置没有刷新
- 服务没有重启

**解决方法**：
- 检查Nacos控制台中的配置
- 重启服务
- 检查配置文件格式

### 2. 仍然不触发重复登录检测

**排查步骤**：
1. 确认配置值：`max-concurrent-sessions: 1`
2. 确认当前会话数：查看日志中的"当前活跃会话数量"
3. 确认同设备过滤：查看"过滤同设备会话后"的日志

### 3. 会话没有被踢掉

**排查步骤**：
1. 检查是否进入了踢掉会话的逻辑
2. 检查数据库更新的影响行数
3. 检查事务是否正确提交

## 监控脚本

### 实时监控配置和会话状态

```bash
#!/bin/bash

echo "=== 重复登录功能监控 ==="

# 1. 检查当前配置（如果有配置接口）
echo "1. 当前配置:"
curl -s http://localhost:9901/ai-bff/api/v1/config/duplicate-login 2>/dev/null | jq . || echo "配置接口不可用"

# 2. 检查数据库会话状态
echo "2. 数据库会话状态:"
psql -d your_db -c "
SELECT 
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_sessions,
    COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_sessions
FROM a_member_session 
WHERE member_id = 4;
"

# 3. 检查Redis缓存状态
echo "3. Redis缓存状态:"
cache_content=$(redis-cli get "bff:member:sessions:id::4")
if [ "$cache_content" != "" ] && [ "$cache_content" != "(nil)" ]; then
    session_count=$(echo "$cache_content" | jq '. | length' 2>/dev/null || echo "解析失败")
    echo "缓存中的会话数量: $session_count"
else
    echo "缓存为空"
fi

echo "=== 监控完成 ==="
```

## 总结

问题的根本原因是配置不当：
- `max-concurrent-sessions: 3` 太大，导致1个会话不会触发重复登录处理
- 需要将其改为 `1` 来测试重复登录功能

修改配置并重启服务后，重复登录功能应该能正常工作。
