# 主动更新缓存方案验证

## 方案说明

### 问题
- 第一次登录时，Redis key `"bff:member:sessions:id::4"` 是空数组
- 第二次登录时，重复登录检测获取到过期的缓存数据

### 解决方案
**主动更新缓存**：在登录成功后，主动从数据库查询最新的会话列表并更新Redis缓存。

### 实现逻辑

1. **登录流程**：
   ```
   重复登录检查 → 创建会话 → 主动更新缓存
   ```

2. **缓存更新**：
   ```java
   // 在 MemberAuthServiceImpl.login() 中
   memberSessionService.createSessionForMember(memberId, request);
   memberSessionService.refreshSessionCache(memberId);  // 新增
   ```

3. **refreshSessionCache 方法**：
   ```java
   // 1. 从数据库查询最新数据
   List<MemberSession> latestSessions = sessionMapper.selectMemberSessionsByMemberId(memberId);
   
   // 2. 主动更新缓存
   sessionListCache.put(cacheKey, latestSessions);
   ```

## 验证步骤

### 1. 清理环境

```sql
-- 清理测试用户的所有会话
DELETE FROM a_member_session WHERE member_id = 4;
```

```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::4"
```

### 2. 第一次登录测试

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**验证点**：

1. **登录前Redis状态**：
   ```bash
   redis-cli exists "bff:member:sessions:id::4"
   # 期望：0 (不存在)
   ```

2. **登录后Redis状态**：
   ```bash
   redis-cli exists "bff:member:sessions:id::4"
   # 期望：1 (存在)
   
   redis-cli get "bff:member:sessions:id::4"
   # 期望：包含1个会话的JSON数组
   ```

3. **数据库状态**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time 
   FROM a_member_session 
   WHERE member_id = 4;
   # 期望：1条 is_active=true 的记录
   ```

4. **日志验证**：
   ```
   用户 4 重复登录检查：当前活跃会话数量 0
   用户 4 首次登录，允许
   为用户 4 创建会话记录成功
   从数据库查询到用户 4 的最新活跃会话数量: 1
   成功更新用户 4 的会话列表缓存，会话数量: 1
   为用户 4 刷新会话列表缓存成功
   ```

### 3. 第二次登录测试

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**验证点**：

1. **登录前Redis状态**：
   ```bash
   redis-cli get "bff:member:sessions:id::4"
   # 期望：包含1个会话的JSON数组（第一次登录后的缓存）
   ```

2. **登录后Redis状态**：
   ```bash
   redis-cli get "bff:member:sessions:id::4"
   # 期望：包含1个会话的JSON数组（新的会话，旧会话被踢掉）
   ```

3. **数据库状态**：
   ```sql
   SELECT id, member_id, ip_address, is_active, login_time, create_time 
   FROM a_member_session 
   WHERE member_id = 4 
   ORDER BY create_time DESC;
   # 期望：2条记录，1条 is_active=false（被踢掉），1条 is_active=true（新登录）
   ```

4. **日志验证**：
   ```
   用户 4 重复登录检查：当前活跃会话数量 1
   活跃会话 - ID: 123, IP: 127.0.0.1, 登录时间: 2024-08-01T10:00:00
   用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
   用户 4 登录时踢掉了 1 个旧会话
   踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T10:00:00
   为用户 4 创建会话记录成功
   从数据库查询到用户 4 的最新活跃会话数量: 1
   成功更新用户 4 的会话列表缓存，会话数量: 1
   为用户 4 刷新会话列表缓存成功
   ```

### 4. 缓存一致性验证

#### 测试缓存与数据库的一致性

```bash
# 第三次登录
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{"identifier": "13800138000", "password": "aaBB@888"}'

# 立即检查缓存和数据库
redis-cli get "bff:member:sessions:id::4" > /tmp/cache_data.json
psql -d your_db -c "SELECT json_agg(row_to_json(t)) FROM (SELECT id, member_id, ip_address, is_active, login_time FROM a_member_session WHERE member_id = 4 AND is_active = true) t;" > /tmp/db_data.json

# 比较两个文件的内容
diff /tmp/cache_data.json /tmp/db_data.json
```

期望：缓存和数据库的数据应该一致

#### 测试并发登录

```bash
# 同时发起多个登录请求
for i in {1..3}; do
  curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
    -H "Content-Type: application/json" \
    -d '{"identifier": "13800138000", "password": "aaBB@888"}' &
done
wait

# 检查最终状态
redis-cli get "bff:member:sessions:id::4"
psql -d your_db -c "SELECT COUNT(*) FROM a_member_session WHERE member_id = 4 AND is_active = true;"
```

期望：
- Redis缓存包含最新的活跃会话
- 数据库中只有1个活跃会话（根据配置的最大会话数）

## 监控脚本

### Redis缓存监控

```bash
#!/bin/bash
# monitor_cache.sh

echo "开始监控Redis缓存变化..."
redis-cli monitor | grep "bff:member:sessions:id::4" | while read line; do
    echo "[$(date)] $line"
    
    # 获取当前缓存内容
    cache_content=$(redis-cli get "bff:member:sessions:id::4")
    echo "当前缓存内容: $cache_content"
    echo "---"
done
```

### 数据库变化监控

```sql
-- 创建触发器监控会话表变化
CREATE OR REPLACE FUNCTION log_session_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        RAISE NOTICE '会话插入: member_id=%, session_id=%, is_active=%', NEW.member_id, NEW.id, NEW.is_active;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.is_active != NEW.is_active THEN
            RAISE NOTICE '会话状态变更: member_id=%, session_id=%, %->%', NEW.member_id, NEW.id, OLD.is_active, NEW.is_active;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 应用触发器
DROP TRIGGER IF EXISTS session_changes_trigger ON a_member_session;
CREATE TRIGGER session_changes_trigger
    AFTER INSERT OR UPDATE ON a_member_session
    FOR EACH ROW EXECUTE FUNCTION log_session_changes();
```

## 性能对比

### 修改前后的性能对比

| 指标 | 修改前 | 修改后 | 改进 |
|------|--------|--------|------|
| 缓存一致性 | 差（可能不一致） | 好（主动更新） | ✅ |
| 重复登录检测准确性 | 低（缓存过期） | 高（实时数据） | ✅ |
| 数据库查询次数 | 少（但数据可能过期） | 适中（按需查询） | ⚖️ |
| 缓存命中率 | 高（但数据可能错误） | 高（数据正确） | ✅ |
| 代码复杂度 | 低 | 中等 | ⚠️ |

### 性能监控指标

1. **缓存更新频率**：
   ```bash
   # 统计缓存更新次数
   grep "成功更新用户.*的会话列表缓存" application.log | wc -l
   ```

2. **数据库查询频率**：
   ```bash
   # 统计会话查询次数
   grep "从数据库查询到用户.*的最新活跃会话数量" application.log | wc -l
   ```

3. **重复登录检测成功率**：
   ```bash
   # 统计重复登录检测次数
   grep "用户.*重复登录检查" application.log | wc -l
   grep "用户.*会话数量超限" application.log | wc -l
   ```

## 总结

### 优势

1. **数据一致性**：缓存与数据库保持实时同步
2. **检测准确性**：重复登录检测基于最新数据
3. **性能平衡**：在数据一致性和性能之间找到平衡

### 注意事项

1. **事务一致性**：确保会话创建和缓存更新在同一事务中
2. **异常处理**：缓存更新失败不应影响登录流程
3. **并发控制**：高并发场景下的缓存更新竞争

### 后续优化

1. **异步更新**：考虑使用异步方式更新缓存
2. **批量更新**：对于批量操作，考虑批量更新缓存
3. **监控告警**：添加缓存更新失败的监控告警
