# 重复登录处理功能说明

## 功能概述

为了提高系统安全性和用户体验，在用户登录流程中增加了重复登录检查和处理功能。该功能可以根据配置策略处理用户在多个设备上的重复登录行为。

## 功能特性

### 1. 灵活的策略配置
- **ALLOW_MULTIPLE**: 允许多设备同时登录，不做任何限制
- **KICK_OLD**: 新登录踢掉旧登录（默认策略）
- **REJECT_NEW**: 拒绝新登录，保护已有会话

### 2. 可配置的会话数量限制
- 支持配置最大同时登录设备数（默认3个）
- 超过限制时根据策略进行处理

### 3. 同设备登录优化
- 可选择是否对同设备的重复登录进行限制
- 默认同设备重复登录不受限制

### 4. 安全的会话管理
- 被踢掉的会话对应的JWT Token会自动加入黑名单
- 确保被踢掉的Token无法继续使用

## 配置说明

在 `ai-bff-dev.yml` 配置文件中添加以下配置：

```yaml
app:
  security:
    duplicate-login:
      # 重复登录处理策略
      strategy: KICK_OLD
      # 最大同时登录设备数
      max-concurrent-sessions: 3
      # 是否检查同设备重复登录
      check-same-device: false
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| strategy | String | KICK_OLD | 重复登录处理策略 |
| max-concurrent-sessions | Integer | 3 | 最大同时登录设备数 |
| check-same-device | Boolean | false | 是否检查同设备重复登录 |

## 实现架构

### 核心组件

1. **IDuplicateLoginService**: 重复登录处理服务接口
2. **DuplicateLoginServiceImpl**: 重复登录处理服务实现
3. **MemberAuthServiceImpl**: 在登录流程中集成重复登录检查
4. **MemberSessionService**: 会话管理服务

### 处理流程

1. 用户发起登录请求
2. 验证用户凭证
3. 检查重复登录情况
4. 根据策略处理重复登录
5. 生成JWT Token
6. 创建新会话记录
7. 返回登录结果

## 使用场景

### 场景1：企业安全要求
- 策略：REJECT_NEW
- 最大会话数：1
- 确保用户只能在一个设备上登录

### 场景2：普通用户体验
- 策略：KICK_OLD
- 最大会话数：3
- 允许多设备登录，但有数量限制

### 场景3：开放式应用
- 策略：ALLOW_MULTIPLE
- 最大会话数：不限制
- 完全开放的多设备登录

## 监控和日志

系统会记录以下关键日志：
- 重复登录检查结果
- 会话踢掉操作
- Token黑名单添加操作

## 注意事项

1. **性能考虑**: 每次登录都会查询用户的活跃会话，建议对会话查询进行缓存优化
2. **Token黑名单**: 被踢掉的Token会加入Redis黑名单，需要确保Redis有足够的存储空间
3. **同设备判断**: 当前使用IP地址进行同设备判断，可根据需要增加更复杂的设备指纹识别
4. **配置热更新**: 配置修改后需要重启服务才能生效

## 扩展建议

1. **设备指纹识别**: 可以集成更复杂的设备指纹识别技术
2. **用户通知**: 可以在踢掉会话时向用户发送通知
3. **管理界面**: 可以为管理员提供会话管理界面
4. **统计分析**: 可以添加重复登录的统计分析功能
