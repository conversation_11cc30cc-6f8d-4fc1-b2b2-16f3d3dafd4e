# 重复登录修复验证脚本

## 修复内容总结

### 1. 修复的问题

1. **SQL查询问题**：`selectMemberSessionsByMemberId` 没有过滤 `is_active = true`
2. **Java代码冗余**：在SQL已经过滤的情况下，Java代码还在重复过滤
3. **缓存清理问题**：会话状态变更时没有正确清理相关缓存

### 2. 修复的文件

- `ai-services/ai-modules/ai-bff/src/main/resources/mapper/v1/member/MemberSessionMapper.xml`
- `ai-services/ai-modules/ai-bff/src/main/java/ai/showlab/bff/service/v1/member/impl/MemberSessionServiceImpl.java`

## 验证步骤

### 1. 清理环境

```sql
-- 清理测试用户的所有会话
DELETE FROM a_member_session WHERE member_id = 4;

-- 确认清理结果
SELECT COUNT(*) FROM a_member_session WHERE member_id = 4;
```

```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::4"
redis-cli del "bff:member:session:token::*"
```

### 2. 重启服务

确保配置生效：
- `check-same-device: true`
- `max-concurrent-sessions: 3`
- `strategy: KICK_OLD`

### 3. 测试重复登录

#### 第一次登录

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -H "X-Real-IP: ************0" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**期望结果**：
- 登录成功
- 数据库中有1条 `is_active=true` 的记录
- 日志显示："用户 4 首次登录，允许"

#### 第二次登录（同IP）

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -H "X-Real-IP: ************0" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

**期望结果**：
- 登录成功
- 数据库中有2条记录：1条 `is_active=false`（被踢掉），1条 `is_active=true`（新登录）
- 日志显示重复登录检测和处理信息

### 4. 验证数据库状态

```sql
-- 查看会话记录
SELECT 
    id, 
    member_id, 
    LEFT(token, 10) as token_prefix,
    ip_address, 
    login_time, 
    is_active, 
    create_time 
FROM a_member_session 
WHERE member_id = 4 
ORDER BY create_time DESC;

-- 查看活跃会话数量
SELECT COUNT(*) as active_sessions 
FROM a_member_session 
WHERE member_id = 4 AND is_active = true;
```

**期望结果**：
- 只有1个 `is_active=true` 的会话
- 其他会话的 `is_active=false`

### 5. 验证日志输出

关键日志信息应该包括：

```
用户 4 重复登录检查：当前活跃会话数量 1
用户 4 当前登录IP: ************0, UserAgent: curl/7.68.0
用户 4 过滤同设备会话后：原始会话数 1, 过滤后会话数 0
用户 4 会话数量超限：当前 1 >= 最大 1, 执行策略 KICK_OLD
用户 4 登录时踢掉了 1 个旧会话
踢掉用户 4 的旧会话，会话ID: 123, 登录时间: 2024-08-01T10:00:00
```

## 测试不同场景

### 场景1：不同IP登录

```bash
# 第一次登录
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -H "X-Real-IP: ************0" \
  -d '{"identifier": "13800138000", "password": "aaBB@888"}'

# 第二次登录（不同IP）
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -H "X-Real-IP: ************1" \
  -d '{"identifier": "13800138000", "password": "aaBB@888"}'
```

**期望结果**：
- 两次登录都成功
- 数据库中有2条 `is_active=true` 的记录（不同IP被认为是不同设备）

### 场景2：超过最大会话数

```bash
# 连续登录4次（超过最大会话数3）
for i in {1..4}; do
  curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
    -H "Content-Type: application/json" \
    -H "X-Real-IP: ************$i" \
    -d '{"identifier": "13800138000", "password": "aaBB@888"}'
  echo "第 $i 次登录完成"
  sleep 1
done
```

**期望结果**：
- 最后只有3个活跃会话
- 最旧的会话被踢掉

## 故障排除

### 1. 如果重复登录检测不工作

**检查项**：
- 配置是否生效：`check-same-device: true`
- 服务是否重启
- `IDuplicateLoginService` 是否正确注入

### 2. 如果查询不到活跃会话

**检查项**：
- SQL查询是否包含 `is_active = true`
- 缓存是否已清理
- 数据库中的数据是否正确

### 3. 如果缓存没有清理

**检查项**：
- `@CacheEvict` 注解是否生效
- Redis连接是否正常
- 缓存key是否正确

## 性能监控

### 监控指标

1. **会话查询性能**：
   ```sql
   EXPLAIN ANALYZE 
   SELECT * FROM a_member_session 
   WHERE member_id = 4 AND is_active = true AND delete_time IS NULL;
   ```

2. **缓存命中率**：
   - 监控 `bff:member:sessions:id` 的命中率
   - 监控 `bff:member:session:token` 的命中率

3. **重复登录频率**：
   - 统计每天的重复登录次数
   - 分析重复登录的模式

### 建议的索引

```sql
-- 为会话查询创建复合索引
CREATE INDEX IF NOT EXISTS idx_member_session_active 
ON a_member_session (member_id, is_active, delete_time) 
WHERE delete_time IS NULL;

-- 为Token查询创建索引
CREATE INDEX IF NOT EXISTS idx_member_session_token 
ON a_member_session (token) 
WHERE is_active = true AND delete_time IS NULL;
```

## 总结

修复后的重复登录功能应该能够：

1. **正确检测重复登录**：通过改进的SQL查询和缓存管理
2. **准确执行策略**：根据配置正确踢掉旧会话或拒绝新登录
3. **详细的日志记录**：便于调试和监控
4. **高效的性能**：通过合理的缓存和索引优化
