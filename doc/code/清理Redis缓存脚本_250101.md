# Redis缓存清理脚本

## 背景

由于修改了Redis序列化器（从GenericJackson2JsonRedisSerializer改为FastJson2JsonRedisSerializer），需要清理现有的缓存数据以避免反序列化错误。

## 清理脚本

### 方法1：清理所有bff相关缓存

```bash
# 连接到Redis
redis-cli

# 查看所有bff相关的key
KEYS bff:*

# 删除所有bff相关的缓存
redis-cli --scan --pattern "bff:*" | xargs redis-cli del
```

### 方法2：清理特定的缓存

```bash
# 清理会话相关缓存
redis-cli del "bff:member:sessions:id::*"
redis-cli del "bff:member:session:token::*"

# 清理其他可能的缓存
redis-cli del "bff:member:profile::*"
redis-cli del "bff:member:info::*"
redis-cli del "bff:member:auth::*"
```

### 方法3：使用通配符批量删除

```bash
# 删除会话列表缓存
redis-cli --eval "return redis.call('del', unpack(redis.call('keys', ARGV[1])))" , "bff:member:sessions:id::*"

# 删除会话token缓存
redis-cli --eval "return redis.call('del', unpack(redis.call('keys', ARGV[1])))" , "bff:member:session:token::*"
```

### 方法4：完整的清理脚本

创建一个shell脚本 `clear_bff_cache.sh`：

```bash
#!/bin/bash

echo "开始清理BFF模块的Redis缓存..."

# Redis连接信息
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD:-}

# 构建redis-cli命令
if [ -n "$REDIS_PASSWORD" ]; then
    REDIS_CMD="redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD"
else
    REDIS_CMD="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
fi

echo "连接到Redis: $REDIS_HOST:$REDIS_PORT"

# 清理会话相关缓存
echo "清理会话缓存..."
$REDIS_CMD --scan --pattern "bff:member:sessions:id::*" | xargs -r $REDIS_CMD del
$REDIS_CMD --scan --pattern "bff:member:session:token::*" | xargs -r $REDIS_CMD del

# 清理用户信息缓存
echo "清理用户信息缓存..."
$REDIS_CMD --scan --pattern "bff:member:profile::*" | xargs -r $REDIS_CMD del
$REDIS_CMD --scan --pattern "bff:member:info::*" | xargs -r $REDIS_CMD del
$REDIS_CMD --scan --pattern "bff:member:auth::*" | xargs -r $REDIS_CMD del

# 清理其他BFF缓存
echo "清理其他BFF缓存..."
$REDIS_CMD --scan --pattern "bff:*" | xargs -r $REDIS_CMD del

echo "缓存清理完成！"

# 验证清理结果
echo "验证清理结果..."
REMAINING_KEYS=$($REDIS_CMD --scan --pattern "bff:*" | wc -l)
echo "剩余的bff相关key数量: $REMAINING_KEYS"

if [ $REMAINING_KEYS -eq 0 ]; then
    echo "✅ 所有BFF缓存已成功清理"
else
    echo "⚠️  仍有 $REMAINING_KEYS 个bff相关的key未清理"
    echo "剩余的key:"
    $REDIS_CMD --scan --pattern "bff:*"
fi
```

## 使用方法

### 1. 直接执行命令

```bash
# 最简单的方法
redis-cli --scan --pattern "bff:*" | xargs redis-cli del
```

### 2. 使用脚本

```bash
# 给脚本执行权限
chmod +x clear_bff_cache.sh

# 执行脚本
./clear_bff_cache.sh

# 或者指定Redis连接信息
REDIS_HOST=your-redis-host REDIS_PORT=6379 REDIS_PASSWORD=your-password ./clear_bff_cache.sh
```

## 注意事项

1. **备份重要数据**：如果有重要的缓存数据，请先备份
2. **生产环境谨慎操作**：在生产环境执行前请先在测试环境验证
3. **业务影响**：清理缓存后，首次访问可能会稍慢，因为需要重新从数据库加载数据
4. **监控告警**：清理缓存可能会触发缓存未命中的告警，这是正常现象

## 验证清理效果

清理完成后，可以通过以下方式验证：

```bash
# 检查是否还有bff相关的key
redis-cli KEYS "bff:*"

# 检查特定的缓存key
redis-cli EXISTS "bff:member:sessions:id::1"
redis-cli EXISTS "bff:member:session:token::some-token"
```

## 重新测试

清理缓存后，建议进行以下测试：

1. **重复登录测试**：验证重复登录功能是否正常
2. **会话管理测试**：验证会话创建、查询、终止功能
3. **缓存功能测试**：验证缓存是否正常工作
4. **性能测试**：确认缓存命中率恢复正常
