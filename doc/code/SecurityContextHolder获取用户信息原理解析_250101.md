# SecurityContextHolder获取用户信息原理解析

## 问题
```java
LoginMember loginMember = (LoginMember) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
```
这行代码是如何能够获取到当前登录用户信息的？

## 完整工作流程

### 1. 用户登录阶段

#### 登录请求处理 (MemberAuthServiceImpl.login)
```java
// 1. 验证用户凭证
MemberAuth auth = verifyCredential(authType, identifier, password);

// 2. 生成JWT Token，包含用户信息
Map<String, Object> claims = new HashMap<>();
claims.put("memberId", auth.getMemberId());
claims.put("username", getUsername(auth, identifier, authType));
claims.put("nickname", member.getNickname());
claims.put("authType", authType);
String token = jwtTokenProvider.generateToken(String.valueOf(auth.getMemberId()), claims);

// 3. 返回Token给客户端
return token;
```

**关键点**：JWT Token中包含了用户的基本信息（memberId, username等）

### 2. 后续请求阶段

#### 客户端发送请求
```http
GET /api/v1/member/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI0IiwibWVtYmVySWQiOjQsInVzZXJuYW1lIjoiMTM4MDAxMzgwMDAiLCJuaWNrbmFtZSI6IuaWsOeUqOaItyIsImF1dGhUeXBlIjoyLCJpYXQiOjE3MzU3MDY0NDAsImV4cCI6MTczODI5ODQ0MH0.xxx
```

#### JWT认证过滤器处理 (JwtAuthenticationFilter)

```java
@Override
protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                FilterChain filterChain) throws ServletException, IOException {
    // 1. 从请求头获取JWT Token
    String jwt = getJwtFromRequest(request); // 解析 "Bearer token"
    
    if (StringUtils.hasText(jwt)) {
        // 2. 检查Token是否在黑名单中
        if (redisTemplate.hasKey(CacheConstants.BFF_TOKEN_BLACKLIST_KEY + ":" + jwt)) {
            // Token已被拉黑，拒绝访问
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }

        // 3. 验证JWT Token的有效性（签名、过期时间等）
        if (jwtTokenProvider.validateToken(jwt)) {
            // 4. 解析JWT Token中的Claims（用户信息）
            Claims claims = jwtTokenProvider.getClaimsFromToken(jwt);
            Long memberId = Long.parseLong(claims.getSubject()); // 从subject获取用户ID
            String username = (String) claims.get("username");   // 从claims获取用户名

            // 5. 创建LoginMember对象
            LoginMember loginMember = new LoginMember(memberId, username, jwt, "member");

            // 6. 创建Spring Security的Authentication对象
            UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(loginMember, null, null);
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

            // 7. 将Authentication设置到SecurityContext中
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
    }
    
    // 8. 继续执行后续的过滤器和控制器
    filterChain.doFilter(request, response);
}
```

#### 控制器中获取用户信息

```java
@GetMapping("/profile")
public ResponseEntity<RestResult> getProfile() {
    // 从SecurityContext中获取当前认证信息
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    
    // 获取Principal（在第6步中设置的LoginMember对象）
    LoginMember loginMember = (LoginMember) authentication.getPrincipal();
    
    // 现在可以使用用户信息
    Long memberId = loginMember.getMemberId();
    String username = loginMember.getUsername();
    
    // 业务逻辑...
}
```

## 核心组件说明

### 1. LoginMember类
```java
@Data
@NoArgsConstructor
public class LoginMember implements Serializable {
    private Long memberId;     // 会员ID
    private String username;   // 用户名
    private String token;      // JWT Token
    private String userType;   // 用户类型
}
```

**作用**：封装当前登录用户的基本信息

### 2. JwtAuthenticationFilter类
```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    // JWT Token验证和用户信息设置
}
```

**作用**：
- 拦截每个HTTP请求
- 验证JWT Token
- 将用户信息设置到SecurityContext中

### 3. SecurityContextHolder
```java
// Spring Security提供的线程本地存储
SecurityContextHolder.getContext().setAuthentication(authentication);
SecurityContextHolder.getContext().getAuthentication().getPrincipal();
```

**作用**：
- 线程本地存储，每个请求线程都有独立的SecurityContext
- 存储当前请求的认证信息

## 数据流转图

```
1. 用户登录
   ↓
2. 生成JWT Token (包含用户信息)
   ↓
3. 返回Token给客户端
   ↓
4. 客户端后续请求携带Token
   ↓
5. JwtAuthenticationFilter拦截请求
   ↓
6. 验证Token有效性
   ↓
7. 解析Token中的用户信息
   ↓
8. 创建LoginMember对象
   ↓
9. 设置到SecurityContext中
   ↓
10. 控制器中通过SecurityContextHolder获取用户信息
```

## 关键技术点

### 1. 线程本地存储 (ThreadLocal)
```java
// SecurityContextHolder内部使用ThreadLocal
private static final ThreadLocal<SecurityContext> contextHolder = new ThreadLocal<>();
```

**特点**：
- 每个请求线程都有独立的SecurityContext
- 线程安全，不会出现用户信息混乱
- 请求结束后自动清理

### 2. JWT Token结构
```json
{
  "sub": "4",                    // subject: 用户ID
  "memberId": 4,                 // 自定义claim: 会员ID
  "username": "13800138000",     // 自定义claim: 用户名
  "nickname": "新用户",          // 自定义claim: 昵称
  "authType": 2,                 // 自定义claim: 认证类型
  "iat": 1735706440,             // issued at: 签发时间
  "exp": 1738298440              // expiration: 过期时间
}
```

### 3. Spring Security认证流程
```java
// 1. 创建Authentication对象
UsernamePasswordAuthenticationToken authentication = 
    new UsernamePasswordAuthenticationToken(principal, credentials, authorities);

// 2. 设置到SecurityContext
SecurityContextHolder.getContext().setAuthentication(authentication);

// 3. 后续获取
Authentication auth = SecurityContextHolder.getContext().getAuthentication();
Object principal = auth.getPrincipal(); // 这就是第1步设置的principal
```

## 安全机制

### 1. Token黑名单
```java
// 检查Token是否被拉黑（登出时会加入黑名单）
if (redisTemplate.hasKey(CacheConstants.BFF_TOKEN_BLACKLIST_KEY + ":" + jwt)) {
    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
    return;
}
```

### 2. Token有效性验证
```java
// 验证签名、过期时间等
if (jwtTokenProvider.validateToken(jwt)) {
    // Token有效，继续处理
} else {
    // Token无效，拒绝访问
    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
    return;
}
```

### 3. 会话管理
```java
// 登录时创建会话记录
memberSessionService.createSessionForMember(memberId, request);

// 登出时终止会话
memberSessionService.terminateAllSessions(memberId);
```

## 使用示例

### 在控制器中获取当前用户
```java
@RestController
public class MemberController {
    
    @GetMapping("/profile")
    public ResponseEntity<RestResult> getProfile() {
        // 方式1：直接获取
        LoginMember loginMember = (LoginMember) SecurityContextHolder
            .getContext().getAuthentication().getPrincipal();
        
        // 方式2：安全获取（推荐）
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof LoginMember) {
            LoginMember loginMember = (LoginMember) authentication.getPrincipal();
            // 使用用户信息...
        }
        
        return ResponseEntity.ok(RestResult.success());
    }
}
```

### 在服务层获取当前用户
```java
@Service
public class SomeService {
    
    public void doSomething() {
        try {
            LoginMember loginMember = (LoginMember) SecurityContextHolder
                .getContext().getAuthentication().getPrincipal();
            Long memberId = loginMember.getMemberId();
            // 业务逻辑...
        } catch (Exception e) {
            // 处理未登录或类型转换异常
            throw new BusinessException("用户未登录");
        }
    }
}
```

## 总结

`SecurityContextHolder.getContext().getAuthentication().getPrincipal()` 能够获取到用户信息的原理是：

1. **登录时**：JWT Token中包含了用户信息
2. **请求时**：JwtAuthenticationFilter解析Token并创建LoginMember对象
3. **存储**：将LoginMember设置为Authentication的Principal
4. **获取**：通过SecurityContextHolder从线程本地存储中获取

这是一个典型的**无状态认证**机制，用户信息存储在JWT Token中，每次请求都会重新解析和设置，不依赖服务器端的Session存储。
