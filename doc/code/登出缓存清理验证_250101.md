# 登出缓存清理验证

## 问题描述

退出登录后，Redis中的缓存key没有被删除，包括：
- `bff:member:sessions:id::4` - 会话列表缓存
- `bff:member:profile::4` - 用户信息缓存

## 修复内容

### 1. 新增缓存清理方法

在 `MemberAuthServiceImpl` 中添加了 `clearUserCaches` 方法：

```java
private void clearUserCaches(Long memberId) {
    // 1. 清除用户信息缓存
    Cache profileCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_PROFILE_KEY);
    if (profileCache != null) {
        profileCache.evict(memberId);
    }

    // 2. 清除用户认证信息缓存
    Cache authCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_AUTH_KEY);
    if (authCache != null) {
        authCache.evict(memberId);
    }

    // 3. 清除会话列表缓存
    Cache sessionCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY);
    if (sessionCache != null) {
        sessionCache.evict(memberId);
    }
}
```

### 2. 修改登出流程

在登出方法中调用缓存清理：

```java
// 2. 终止所有会话记录
memberSessionService.terminateAllSessions(memberId);
memberSessionService.refreshSessionCache(memberId);

// 3. 清除用户相关的所有缓存（新增）
clearUserCaches(memberId);
log.info("已清除用户 {} 的所有缓存", memberId);
```

## 验证步骤

### 1. 准备测试环境

```bash
# 清理现有缓存
redis-cli del "bff:member:sessions:id::4"
redis-cli del "bff:member:profile::4"
redis-cli del "bff:member:auth::4"
```

### 2. 执行登录

```java
// 使用PostmanTokenGenerator或TestBffLogin登录
String token = PostmanTokenGenerator.quickGenerate("13800138000", "aaBB@888");
```

### 3. 检查登录后的缓存

```bash
# 检查会话缓存
redis-cli get "bff:member:sessions:id::4"
# 期望：包含会话信息的JSON数组

# 检查用户信息缓存
redis-cli get "bff:member:profile::4"
# 期望：包含用户信息的JSON对象

# 检查认证信息缓存
redis-cli get "bff:member:auth::4"
# 期望：包含认证信息的JSON数组
```

### 4. 执行登出

```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN"
```

### 5. 检查登出后的缓存

```bash
# 检查会话缓存
redis-cli get "bff:member:sessions:id::4"
# 期望：空数组 [] 或不存在

# 检查用户信息缓存
redis-cli get "bff:member:profile::4"
# 期望：不存在 (nil)

# 检查认证信息缓存
redis-cli get "bff:member:auth::4"
# 期望：不存在 (nil)

# 检查Token黑名单
redis-cli get "bff:token:blacklist:$TOKEN"
# 期望：存在且值为 "blacklisted"
```

## 自动化验证脚本

### 1. 完整验证脚本

```bash
#!/bin/bash

echo "=== 登出缓存清理验证 ==="

USER_ID=4
PHONE="13800138000"
PASSWORD="aaBB@888"
BASE_URL="http://localhost:9901/ai-bff"

# 1. 清理初始环境
echo "1. 清理初始环境..."
redis-cli del "bff:member:sessions:id::$USER_ID"
redis-cli del "bff:member:profile::$USER_ID"
redis-cli del "bff:member:auth::$USER_ID"

# 2. 执行登录
echo "2. 执行登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/member/login" \
  -H "Content-Type: application/json" \
  -d "{\"identifier\":\"$PHONE\",\"password\":\"$PASSWORD\"}")

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data')

if [ "$TOKEN" == "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 登录失败"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ 登录成功，Token: ${TOKEN:0:20}..."

# 3. 触发缓存生成（调用需要缓存的接口）
echo "3. 触发缓存生成..."

# 调用profile接口生成用户信息缓存
curl -s -X POST "$BASE_URL/api/v1/member/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" > /dev/null

# 等待缓存生成
sleep 1

# 4. 检查登录后的缓存状态
echo "4. 检查登录后的缓存状态..."

SESSIONS_CACHE=$(redis-cli get "bff:member:sessions:id::$USER_ID")
PROFILE_CACHE=$(redis-cli get "bff:member:profile::$USER_ID")
AUTH_CACHE=$(redis-cli get "bff:member:auth::$USER_ID")

echo "会话缓存: ${SESSIONS_CACHE:0:50}..."
echo "用户信息缓存: ${PROFILE_CACHE:0:50}..."
echo "认证信息缓存: ${AUTH_CACHE:0:50}..."

# 5. 执行登出
echo "5. 执行登出..."
LOGOUT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/member/logout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN")

echo "登出响应: $LOGOUT_RESPONSE"

# 等待缓存清理
sleep 1

# 6. 检查登出后的缓存状态
echo "6. 检查登出后的缓存状态..."

SESSIONS_CACHE_AFTER=$(redis-cli get "bff:member:sessions:id::$USER_ID")
PROFILE_CACHE_AFTER=$(redis-cli get "bff:member:profile::$USER_ID")
AUTH_CACHE_AFTER=$(redis-cli get "bff:member:auth::$USER_ID")
TOKEN_BLACKLIST=$(redis-cli get "bff:token:blacklist:$TOKEN")

echo "会话缓存: $SESSIONS_CACHE_AFTER"
echo "用户信息缓存: $PROFILE_CACHE_AFTER"
echo "认证信息缓存: $AUTH_CACHE_AFTER"
echo "Token黑名单: $TOKEN_BLACKLIST"

# 7. 验证结果
echo "7. 验证结果..."

SUCCESS=true

# 检查会话缓存是否为空数组
if [ "$SESSIONS_CACHE_AFTER" != "[]" ] && [ "$SESSIONS_CACHE_AFTER" != "(nil)" ]; then
    echo "❌ 会话缓存未正确清理"
    SUCCESS=false
else
    echo "✅ 会话缓存已清理"
fi

# 检查用户信息缓存是否被删除
if [ "$PROFILE_CACHE_AFTER" != "(nil)" ]; then
    echo "❌ 用户信息缓存未正确清理"
    SUCCESS=false
else
    echo "✅ 用户信息缓存已清理"
fi

# 检查认证信息缓存是否被删除
if [ "$AUTH_CACHE_AFTER" != "(nil)" ]; then
    echo "❌ 认证信息缓存未正确清理"
    SUCCESS=false
else
    echo "✅ 认证信息缓存已清理"
fi

# 检查Token是否在黑名单中
if [ "$TOKEN_BLACKLIST" == "blacklisted" ]; then
    echo "✅ Token已加入黑名单"
else
    echo "❌ Token未正确加入黑名单"
    SUCCESS=false
fi

# 8. 验证Token是否失效
echo "8. 验证Token是否失效..."
PROFILE_AFTER_LOGOUT=$(curl -s -X POST "$BASE_URL/api/v1/member/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN")

if echo "$PROFILE_AFTER_LOGOUT" | grep -q "401\|Unauthorized\|Token已失效"; then
    echo "✅ Token已失效"
else
    echo "❌ Token仍然有效"
    echo "响应: $PROFILE_AFTER_LOGOUT"
    SUCCESS=false
fi

# 9. 总结
echo "9. 总结..."
if [ "$SUCCESS" = true ]; then
    echo "🎉 所有验证通过，登出缓存清理功能正常！"
else
    echo "💥 验证失败，存在问题需要修复！"
    exit 1
fi
```

### 2. 简化验证脚本

```bash
#!/bin/bash

# 简化版验证脚本
USER_ID=4

echo "=== 检查用户 $USER_ID 的缓存状态 ==="

echo "会话缓存:"
redis-cli get "bff:member:sessions:id::$USER_ID"

echo "用户信息缓存:"
redis-cli get "bff:member:profile::$USER_ID"

echo "认证信息缓存:"
redis-cli get "bff:member:auth::$USER_ID"

echo "所有bff相关的key:"
redis-cli keys "bff:*$USER_ID*"
```

## 手动验证步骤

### 1. 登录并生成缓存

```bash
# 1. 登录
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{"identifier":"13800138000","password":"aaBB@888"}'

# 2. 使用返回的Token调用profile接口（生成缓存）
curl -X POST http://localhost:9901/ai-bff/api/v1/member/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 检查缓存
redis-cli keys "bff:*4*"
```

### 2. 登出并验证缓存清理

```bash
# 1. 登出
curl -X POST http://localhost:9901/ai-bff/api/v1/member/logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. 检查缓存是否被清理
redis-cli keys "bff:*4*"

# 3. 验证Token是否失效
curl -X POST http://localhost:9901/ai-bff/api/v1/member/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 期望结果

### 登录后
- `bff:member:sessions:id::4`: 包含会话信息
- `bff:member:profile::4`: 包含用户信息
- `bff:member:auth::4`: 包含认证信息

### 登出后
- `bff:member:sessions:id::4`: 空数组 `[]` 或不存在
- `bff:member:profile::4`: 不存在 `(nil)`
- `bff:member:auth::4`: 不存在 `(nil)`
- `bff:token:blacklist:TOKEN`: 存在且值为 `blacklisted`

## 故障排除

### 1. 缓存没有被清理

**可能原因**：
- CacheManager注入失败
- 缓存key不匹配
- 异常被捕获但没有抛出

**解决方法**：
- 检查日志中的缓存清理信息
- 验证CacheManager是否正确注入
- 检查缓存key的命名规则

### 2. Token仍然有效

**可能原因**：
- Token黑名单没有生效
- JWT验证逻辑有问题

**解决方法**：
- 检查Token是否在黑名单中
- 验证JWT验证过滤器的逻辑

### 3. 会话缓存没有更新

**可能原因**：
- refreshSessionCache方法没有正确执行
- 数据库会话状态没有更新

**解决方法**：
- 检查数据库中的会话状态
- 验证refreshSessionCache的实现
