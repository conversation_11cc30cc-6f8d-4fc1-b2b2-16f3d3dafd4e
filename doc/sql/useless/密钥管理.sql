-- ====================================================================================
-- AIShowLab - 模型API密钥管理模块数据库架构脚本 (PostgreSQL) v1.2
-- 作者: Gemini
-- 描述: 本脚本定义了模型供应商 (a_model_provider) 和模型API密钥 (a_model_api_key) 的表结构。
--      该设计支持代理接入点、优先级和负载均衡策略。
-- ====================================================================================

-- ==================================================
-- 1. 模型供应商表 (a_model_provider)
-- ==================================================
CREATE TABLE a_model_provider (
    id                      BIGSERIAL PRIMARY KEY,
    provider_name           VARCHAR(100) NOT NULL,
    provider_key            VARCHAR(50) NOT NULL UNIQUE,
    base_url                TEXT,
    description             TEXT,
    create_by               VARCHAR(64),
    update_by               VARCHAR(64),
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE a_model_provider IS '模型供应商表，用于存储不同AI模型供应商的基础信息。';
COMMENT ON COLUMN a_model_provider.id IS '主键ID';
COMMENT ON COLUMN a_model_provider.provider_name IS '供应商名称，用于显示 (例如: OpenAI, Google)';
COMMENT ON COLUMN a_model_provider.provider_key IS '供应商唯一标识，用于程序内部关联 (例如: openai, google)';
COMMENT ON COLUMN a_model_provider.base_url IS '供应商基础API URL';
COMMENT ON COLUMN a_model_provider.description IS '备注信息';
COMMENT ON COLUMN a_model_provider.create_by IS '创建人';
COMMENT ON COLUMN a_model_provider.update_by IS '修改人';
COMMENT ON COLUMN a_model_provider.create_time IS '创建时间';
COMMENT ON COLUMN a_model_provider.update_time IS '更新时间';


-- ==================================================
-- 2. 模型API密钥表 (a_model_api_key)
-- ==================================================
CREATE TABLE a_model_api_key (
    id                      BIGSERIAL PRIMARY KEY,
    provider_id             BIGINT NOT NULL,
    api_key                 TEXT NOT NULL,
    api_endpoint_override   TEXT,
    priority                INTEGER DEFAULT 100,
    weight                  INTEGER DEFAULT 100,
    quota                   NUMERIC(12, 4) DEFAULT 0,
    used_quota              NUMERIC(12, 4) DEFAULT 0,
    status                  VARCHAR(50) NOT NULL DEFAULT 'inactive',
    description             TEXT,
    last_used_time          TIMESTAMPTZ,
    delete_time             TIMESTAMPTZ,
    create_by               VARCHAR(64),
    update_by               VARCHAR(64),
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT fk_model_api_key_provider FOREIGN KEY (provider_id) REFERENCES a_model_provider(id)
);

-- 创建索引
CREATE INDEX idx_model_api_key_provider_id_status ON a_model_api_key(provider_id, status);
CREATE INDEX idx_model_api_key_priority ON a_model_api_key(priority);

-- 添加注释
COMMENT ON TABLE a_model_api_key IS '模型API密钥表，用于统一存储、管理和监控所有第三方服务的API密钥。';
COMMENT ON COLUMN a_model_api_key.id IS '主键ID';
COMMENT ON COLUMN a_model_api_key.provider_id IS '供应商ID，关联 a_model_provider.id';
COMMENT ON COLUMN a_model_api_key.api_key IS 'API密钥的加密存储值。必须在应用层加密后存储，严禁明文。';
COMMENT ON COLUMN a_model_api_key.api_endpoint_override IS '代理接入点URL。若非空，则优先使用此URL代替供应商默认地址。';
COMMENT ON COLUMN a_model_api_key.priority IS '优先级，数字越小，优先级越高。用于主备密钥策略。';
COMMENT ON COLUMN a_model_api_key.weight IS '权重，用于在相同优先级下进行加权轮询。';
COMMENT ON COLUMN a_model_api_key.quota IS '总额度，单位通常为美元或人民币。';
COMMENT ON COLUMN a_model_api_key.used_quota IS '已用额度，由后台任务或调用逻辑更新。';
COMMENT ON COLUMN a_model_api_key.status IS '密钥状态 (字典: api_key_status)，如: active, inactive, exhausted';
COMMENT ON COLUMN a_model_api_key.description IS '备注，可记录密钥来源、用途等信息。';
COMMENT ON COLUMN a_model_api_key.last_used_time IS '该密钥最近一次被使用的时间。';
COMMENT ON COLUMN a_model_api_key.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_api_key.create_by IS '创建人';
COMMENT ON COLUMN a_model_api_key.update_by IS '修改人';
COMMENT ON COLUMN a_model_api_key.create_time IS '创建时间';
COMMENT ON COLUMN a_model_api_key.update_time IS '更新时间';


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
-- 确保函数存在。如果其他脚本已创建，则此命令无操作。
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 a_model_api_key 表绑定触发器
CREATE TRIGGER set_model_api_key_update_time
BEFORE UPDATE ON a_model_api_key
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- 为 a_model_provider 表绑定触发器
CREATE TRIGGER set_model_provider_update_time
BEFORE UPDATE ON a_model_provider
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- ==================================================
-- 脚本结束
-- ================================================== 