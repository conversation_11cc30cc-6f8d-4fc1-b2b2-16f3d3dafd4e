-- ====================================================================================
-- AIShowLab - AI 模型管理模块增强脚本 (PostgreSQL) v1.3
-- 作者: Gemini
-- 描述: 本脚本在原有模型管理基础上，增加了对内部自研模型运维和部署的支持。
--      主要包括：增强 a_model 表，新增 a_model_deployment 表。
-- ====================================================================================

-- (可选) 增强 a_billing_usage 表，用于内部成本核算
-- ALTER TABLE a_billing_usage ADD COLUMN IF NOT EXISTS gpu_seconds NUMERIC(10, 2);
-- ALTER TABLE a_billing_usage ADD COLUMN IF NOT EXISTS cpu_seconds NUMERIC(10, 2);
-- COMMENT ON COLUMN a_billing_usage.gpu_seconds IS '本次调用消耗的 GPU 秒';
-- COMMENT ON COLUMN a_billing_usage.cpu_seconds IS '本次调用消耗的 CPU 秒';


-- ==================================================
-- 新增的表和类型
-- ==================================================

-- 删除已存在的自定义类型，以便重新创建
DROP TYPE IF EXISTS deployment_status_enum;

-- 新增部署状态枚举
CREATE TYPE deployment_status_enum AS ENUM ('unloaded', 'loading', 'active', 'failed', 'inactive');
COMMENT ON TYPE deployment_status_enum IS '部署状态: unloaded-未加载, loading-加载中, active-运行中, failed-失败, inactive-已停用';


-- ==================================================
-- 7. 模型部署实例表 (a_model_deployment)
-- ==================================================
CREATE TABLE a_model_deployment (
    id                  BIGSERIAL PRIMARY KEY,
    model_id            BIGINT NOT NULL REFERENCES a_model(id) ON DELETE CASCADE,
    deployment_env      VARCHAR(50) NOT NULL DEFAULT 'production',
    status              deployment_status_enum NOT NULL DEFAULT 'unloaded',
    endpoint_url        TEXT,
    hostname            VARCHAR(255),
    gpu_ids             VARCHAR(100),
    required_vram_gb    INTEGER,
    last_heartbeat_time TIMESTAMPTZ,
    notes               TEXT,
    delete_time         TIMESTAMPTZ,
    create_by           BIGINT,
    update_by           BIGINT,
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_deployment_model_id ON a_model_deployment(model_id);
CREATE INDEX idx_deployment_status ON a_model_deployment(status);
CREATE INDEX idx_deployment_hostname ON a_model_deployment(hostname);
CREATE INDEX idx_deployment_env ON a_model_deployment(deployment_env);

-- 添加注释
COMMENT ON TABLE a_model_deployment IS '模型部署实例表，用于管理和监控内部自研模型的具体运行时实例，包括其部署环境、节点、状态和资源占用等。';
COMMENT ON COLUMN a_model_deployment.id IS '主键ID';
COMMENT ON COLUMN a_model_deployment.model_id IS '外键，关联 a_model.id';
COMMENT ON COLUMN a_model_deployment.deployment_env IS '部署环境 (如: production, staging, test)';
COMMENT ON COLUMN a_model_deployment.status IS '实例的运行时状态';
COMMENT ON COLUMN a_model_deployment.endpoint_url IS '实例的访问地址 (如 http://host:port/v1/chat)';
COMMENT ON COLUMN a_model_deployment.hostname IS '部署的主机名或 Kubernetes 节点名';
COMMENT ON COLUMN a_model_deployment.gpu_ids IS '使用的 GPU ID (如: "0,1")';
COMMENT ON COLUMN a_model_deployment.required_vram_gb IS '该实例运行时所需显存 (GB)';
COMMENT ON COLUMN a_model_deployment.last_heartbeat_time IS '最后心跳时间，由部署实例定期上报，用于监控';
COMMENT ON COLUMN a_model_deployment.notes IS '备注信息';
COMMENT ON COLUMN a_model_deployment.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_deployment.create_by IS '创建人ID';
COMMENT ON COLUMN a_model_deployment.update_by IS '修改人ID';
COMMENT ON COLUMN a_model_deployment.create_time IS '创建时间';
COMMENT ON COLUMN a_model_deployment.update_time IS '更新时间';


-- ==================================================
-- 为新表绑定自动更新时间的触发器
-- ==================================================
CREATE TRIGGER set_model_deployment_update_time
BEFORE UPDATE ON a_model_deployment
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- ==================================================
-- 脚本结束
-- ================================================== 