-- ====================================================================================
-- AIShowLab - 统一 create_by 和 update_by 字段类型
-- 作者: Gemini
-- 描述: 本脚本将所有业务表中用于审计的 create_by 和 update_by 字段的数据类型
--      从 BIGINT 修改为 VARCHAR(64)，以支持更灵活的用户标识符（如兼容UUID或来自其他系统的字符串ID）。
-- ====================================================================================

-- AI 助手模块
ALTER TABLE a_assistant_category ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_assistant_category ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_assistant ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_assistant ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_assistant_param_def ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_assistant_param_def ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_user_assistant ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_user_assistant ALTER COLUMN update_by TYPE VARCHAR(64);

-- OAuth2 认证模块
ALTER TABLE a_oauth_client_details ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_oauth_client_details ALTER COLUMN update_by TYPE VARCHAR(64);

-- 国家地区模块
ALTER TABLE a_country ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_country ALTER COLUMN update_by TYPE VARCHAR(64);

-- 密钥管理模块
ALTER TABLE a_api_key ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_api_key ALTER COLUMN update_by TYPE VARCHAR(64);

-- 模型管理模块
ALTER TABLE a_model_category ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_model_category ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_model ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_model ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_model_feature ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_model_feature ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_model_output_format ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_model_output_format ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_model_visibility ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_model_visibility ALTER COLUMN update_by TYPE VARCHAR(64);

-- 模型调用与日志模块
ALTER TABLE a_log_model_request ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_log_model_request ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_log_model_response ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_log_model_response ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_log_error ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_log_error ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_log_audit ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_log_audit ALTER COLUMN update_by TYPE VARCHAR(64);

-- 知识库模块
ALTER TABLE a_knowledge_base ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_knowledge_base ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_kb_document ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_kb_document ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_kb_document_chunk ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_kb_document_chunk ALTER COLUMN update_by TYPE VARCHAR(64);

-- 计费与消耗模块
ALTER TABLE a_billing_plan ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_plan ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_price ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_price ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_usage ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_usage ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_balance ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_balance ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_transaction ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_transaction ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_coupon ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_coupon ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_package ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_package ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_order ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_order ALTER COLUMN update_by TYPE VARCHAR(64);

ALTER TABLE a_billing_redemption ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_billing_redemption ALTER COLUMN update_by TYPE VARCHAR(64);

-- 语言模块
ALTER TABLE a_lang ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_lang ALTER COLUMN update_by TYPE VARCHAR(64);

-- 通用与其它模块
ALTER TABLE a_document ALTER COLUMN create_by TYPE VARCHAR(64);
ALTER TABLE a_document ALTER COLUMN update_by TYPE VARCHAR(64);
