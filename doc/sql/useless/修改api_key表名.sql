-- ====================================================================================
-- AIShowLab - 数据库表结构修改脚本 (PostgreSQL)
-- 版本: v1.1
-- 描述: 本脚本用于将 a_api_key 表重命名为 a_model_api_key，并更新相关对象。
-- ====================================================================================

-- 1. 重命名表
ALTER TABLE a_api_key RENAME TO a_model_api_key;

-- 2. 重命名索引
ALTER INDEX idx_api_key_provider_status RENAME TO idx_model_api_key_provider_status;
ALTER INDEX idx_api_key_priority RENAME TO idx_model_api_key_priority;

-- 3. 重命名触发器
ALTER TRIGGER set_api_key_update_time ON a_model_api_key RENAME TO set_model_api_key_update_time;

-- 4. 更新表和列的注释
COMMENT ON TABLE a_model_api_key IS '模型API密钥表，用于统一存储、管理和监控所有第三方服务的API密钥。';
COMMENT ON COLUMN a_model_api_key.id IS '主键ID';
COMMENT ON COLUMN a_model_api_key.provider IS '供应商标识 (字典: model_provider)，如: openai, anthropic';
COMMENT ON COLUMN a_model_api_key.api_key IS 'API密钥的加密存储值。必须在应用层加密后存储，严禁明文。';
COMMENT ON COLUMN a_model_api_key.api_endpoint_override IS '代理接入点URL。若非空，则优先使用此URL代替供应商默认地址。';
COMMENT ON COLUMN a_model_api_key.priority IS '优先级，数字越小，优先级越高。用于主备密钥策略。';
COMMENT ON COLUMN a_model_api_key.weight IS '权重，用于在相同优先级下进行加权轮询。';
COMMENT ON COLUMN a_model_api_key.quota IS '总额度，单位通常为美元或人民币。';
COMMENT ON COLUMN a_model_api_key.used_quota IS '已用额度，由后台任务或调用逻辑更新。';
COMMENT ON COLUMN a_model_api_key.status IS '密钥状态 (字典: api_key_status)，如: active, inactive, exhausted';
COMMENT ON COLUMN a_model_api_key.description IS '备注，可记录密钥来源、用途等信息。';
COMMENT ON COLUMN a_model_api_key.last_used_time IS '该密钥最近一次被使用的时间。';
COMMENT ON COLUMN a_model_api_key.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_api_key.create_by IS '创建人ID';
COMMENT ON COLUMN a_model_api_key.update_by IS '修改人ID';
COMMENT ON COLUMN a_model_api_key.create_time IS '创建时间';
COMMENT ON COLUMN a_model_api_key.update_time IS '更新时间';

-- ==================================================
-- 脚本结束
-- ==================================================
