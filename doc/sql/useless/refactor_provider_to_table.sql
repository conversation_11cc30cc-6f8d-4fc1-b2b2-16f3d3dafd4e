-- ====================================================================================
-- AIShowLab - 数据库表结构修改脚本 (PostgreSQL)
-- 版本: v1.2
-- 描述: 本脚本用于将 a_model_api_key.provider 字段重构为一张独立的 a_model_provider 表，
--      并建立外键关联，以实现更规范的数据管理。
-- ====================================================================================

-- ==================================================
-- 1. 创建 a_model_provider 表
-- ==================================================
CREATE TABLE a_model_provider (
    id                      BIGSERIAL PRIMARY KEY,
    provider_name           VARCHAR(100) NOT NULL,
    provider_key            VARCHAR(50) NOT NULL UNIQUE,
    base_url                TEXT,
    description             TEXT,
    create_by               BIGINT,
    update_by               BIGINT,
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE a_model_provider IS '模型供应商表，用于存储不同AI模型供应商的基础信息。';
COMMENT ON COLUMN a_model_provider.id IS '主键ID';
COMMENT ON COLUMN a_model_provider.provider_name IS '供应商名称，用于显示 (例如: OpenAI, Google)';
COMMENT ON COLUMN a_model_provider.provider_key IS '供应商唯一标识，用于程序内部关联 (例如: openai, google)';
COMMENT ON COLUMN a_model_provider.base_url IS '供应商基础API URL';
COMMENT ON COLUMN a_model_provider.description IS '备注信息';
COMMENT ON COLUMN a_model_provider.create_by IS '创建人ID';
COMMENT ON COLUMN a_model_provider.update_by IS '修改人ID';
COMMENT ON COLUMN a_model_provider.create_time IS '创建时间';
COMMENT ON COLUMN a_model_provider.update_time IS '更新时间';

-- 为 a_model_provider 表绑定更新时间触发器 (假设 trigger_set_update_time 函数已存在)
CREATE TRIGGER set_model_provider_update_time
BEFORE UPDATE ON a_model_provider
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- ==================================================
-- 2. 从 a_model_api_key 迁移数据到 a_model_provider
-- ==================================================
INSERT INTO a_model_provider (provider_name, provider_key)
SELECT DISTINCT provider, provider
FROM a_model_api_key
WHERE provider IS NOT NULL
ON CONFLICT (provider_key) DO NOTHING;

-- ==================================================
-- 3. 修改 a_model_api_key 表结构
-- ==================================================
-- a. 添加 provider_id 列
ALTER TABLE a_model_api_key ADD COLUMN provider_id BIGINT;

-- b. 根据旧的 provider 字符串更新 provider_id 的值
UPDATE a_model_api_key ak
SET provider_id = mp.id
FROM a_model_provider mp
WHERE ak.provider = mp.provider_key;

-- c. 添加外键约束
ALTER TABLE a_model_api_key
ADD CONSTRAINT fk_model_api_key_provider
FOREIGN KEY (provider_id) REFERENCES a_model_provider(id);

-- d. 设置 provider_id 不可为空 (在数据填充和外键建立后)
ALTER TABLE a_model_api_key ALTER COLUMN provider_id SET NOT NULL;

-- e. 删除旧的 provider 列
ALTER TABLE a_model_api_key DROP COLUMN provider;

-- ==================================================
-- 4. 更新索引和注释
-- ==================================================
-- a. 删除依赖于旧 provider 列的索引
DROP INDEX idx_model_api_key_provider_status;

-- b. 创建基于 provider_id 的新索引
CREATE INDEX idx_model_api_key_provider_id_status ON a_model_api_key(provider_id, status);

-- c. 更新 a_model_api_key.provider_id 的注释
COMMENT ON COLUMN a_model_api_key.provider_id IS '供应商ID，关联 a_model_provider.id';

-- ==================================================
-- 脚本结束
-- ================================================== 