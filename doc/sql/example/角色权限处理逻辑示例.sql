-- =====================================================
-- AIShowLab 角色权限管理 SQL 操作脚本
-- 作者: Claude
-- 描述: 本脚本提供角色权限相关表的增删查改以及权限判断的详细SQL实现
-- =====================================================

-- =====================================================
-- 1. 角色管理 (a_base_role)
-- =====================================================

-- 1.1 新增角色
INSERT INTO a_base_role (
    parent_role_id, code, name, description, is_system,
    create_by, update_by
) VALUES (
    NULL, -- 如果有父角色，则填写父角色ID
    'user_vip', -- 角色编码，唯一标识
    'VIP用户', -- 角色名称
    'VIP会员用户，拥有高级功能使用权限', -- 角色描述
    FALSE, -- 是否系统内置角色
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 1.2 修改角色
UPDATE a_base_role
SET name = '高级VIP用户',
    description = '高级VIP会员用户，拥有所有高级功能使用权限',
    update_by = 'admin',
    update_time = NOW()
WHERE code = 'user_vip'
  AND delete_time IS NULL;

-- 1.3 删除角色（软删除）
UPDATE a_base_role
SET delete_time = NOW(),
    update_by = 'admin',
    update_time = NOW()
WHERE code = 'user_vip'
  AND delete_time IS NULL;

-- 1.4 查询角色列表
SELECT id, parent_role_id, code, name, description, is_system, create_time
FROM a_base_role
WHERE delete_time IS NULL
ORDER BY create_time DESC;

-- 1.5 根据ID查询角色详情
SELECT r.id, r.parent_role_id, r.code, r.name, r.description, r.is_system, 
       r.create_time, r.update_time, r.create_by, r.update_by,
       p.id as parent_id, p.code as parent_code, p.name as parent_name
FROM a_base_role r
LEFT JOIN a_base_role p ON r.parent_role_id = p.id
WHERE r.id = 1 AND r.delete_time IS NULL;

-- 1.6 查询角色及其权限
SELECT r.id, r.code, r.name, r.description,
       p.id as permission_id, p.code as permission_code, p.name as permission_name,
       rp.scope as permission_scope, rp.valid_from, rp.valid_to
FROM a_base_role r
LEFT JOIN a_base_role_permission rp ON r.id = rp.role_id AND rp.delete_time IS NULL
LEFT JOIN a_base_permission p ON rp.permission_id = p.id AND p.delete_time IS NULL
WHERE r.id = 1 AND r.delete_time IS NULL;

-- 1.7 查询角色继承树
WITH RECURSIVE role_tree AS (
    -- 基础查询：获取顶级角色（没有父角色的角色）
    SELECT id, parent_role_id, code, name, description, 0 as level
    FROM a_base_role
    WHERE parent_role_id IS NULL AND delete_time IS NULL
    
    UNION ALL
    
    -- 递归查询：获取子角色
    SELECT r.id, r.parent_role_id, r.code, r.name, r.description, rt.level + 1
    FROM a_base_role r
    JOIN role_tree rt ON r.parent_role_id = rt.id
    WHERE r.delete_time IS NULL
)
SELECT * FROM role_tree
ORDER BY level, name;

-- =====================================================
-- 2. 权限组管理 (a_base_permission_group)
-- =====================================================

-- 2.1 新增权限组
INSERT INTO a_base_permission_group (
    code, name, description, parent_group_id, sort_order,
    create_by, update_by
) VALUES (
    'model_management', -- 权限组编码
    '模型管理', -- 权限组名称
    '模型管理相关权限', -- 权限组描述
    NULL, -- 父权限组ID
    10, -- 排序值
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 2.2 修改权限组
UPDATE a_base_permission_group
SET name = '模型管理权限',
    description = '所有模型管理相关的权限',
    sort_order = 5,
    update_by = 'admin',
    update_time = NOW()
WHERE code = 'model_management'
  AND delete_time IS NULL;

-- 2.3 删除权限组（软删除）
UPDATE a_base_permission_group
SET delete_time = NOW(),
    update_by = 'admin',
    update_time = NOW()
WHERE code = 'model_management'
  AND delete_time IS NULL;

-- 2.4 查询权限组列表（树形结构）
WITH RECURSIVE group_tree AS (
    -- 基础查询：获取顶级权限组
    SELECT id, code, name, description, parent_group_id, sort_order, 0 as level
    FROM a_base_permission_group
    WHERE parent_group_id IS NULL AND delete_time IS NULL
    
    UNION ALL
    
    -- 递归查询：获取子权限组
    SELECT pg.id, pg.code, pg.name, pg.description, pg.parent_group_id, pg.sort_order, gt.level + 1
    FROM a_base_permission_group pg
    JOIN group_tree gt ON pg.parent_group_id = gt.id
    WHERE pg.delete_time IS NULL
)
SELECT * FROM group_tree
ORDER BY level, sort_order, name;

-- =====================================================
-- 3. 权限管理 (a_base_permission)
-- =====================================================

-- 3.1 新增权限
INSERT INTO a_base_permission (
    group_id, code, name, description, module, is_system,
    create_by, update_by
) VALUES (
    1, -- 权限组ID
    'model.chat.use', -- 权限唯一标识
    '使用聊天模型', -- 权限名称
    '允许使用AI聊天模型进行对话', -- 权限描述
    'model', -- 所属模块
    TRUE, -- 是否系统内置权限
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 3.2 修改权限
UPDATE a_base_permission
SET name = '使用AI聊天模型',
    description = '允许使用各种AI聊天模型进行对话交互',
    update_by = 'admin',
    update_time = NOW()
WHERE code = 'model.chat.use'
  AND delete_time IS NULL;

-- 3.3 删除权限（软删除）
UPDATE a_base_permission
SET delete_time = NOW(),
    update_by = 'admin',
    update_time = NOW()
WHERE code = 'model.chat.use'
  AND delete_time IS NULL;

-- 3.4 查询权限列表
SELECT p.id, p.code, p.name, p.description, p.module, p.is_system,
       pg.id as group_id, pg.code as group_code, pg.name as group_name
FROM a_base_permission p
LEFT JOIN a_base_permission_group pg ON p.group_id = pg.id AND pg.delete_time IS NULL
WHERE p.delete_time IS NULL
ORDER BY pg.sort_order, p.code;

-- 3.5 添加权限依赖关系
INSERT INTO a_base_permission_dependency (
    permission_id, dependency_id, is_required,
    create_by, update_by
) VALUES (
    1, -- 权限ID
    2, -- 依赖权限ID
    TRUE, -- 是否必须依赖
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 3.6 查询权限依赖关系
SELECT p.code as permission_code, p.name as permission_name,
       d.code as dependency_code, d.name as dependency_name,
       pd.is_required
FROM a_base_permission_dependency pd
JOIN a_base_permission p ON pd.permission_id = p.id AND p.delete_time IS NULL
JOIN a_base_permission d ON pd.dependency_id = d.id AND d.delete_time IS NULL
WHERE pd.delete_time IS NULL;

-- =====================================================
-- 4. 角色-权限映射管理 (a_base_role_permission)
-- =====================================================

-- 4.1 为角色分配权限
INSERT INTO a_base_role_permission (
    role_id, permission_id, valid_from, valid_to, scope,
    create_by, update_by
) VALUES (
    1, -- 角色ID
    1, -- 权限ID
    NOW(), -- 权限生效时间，可为NULL表示立即生效
    NULL, -- 权限失效时间，可为NULL表示永不过期
    '{"models": ["gpt-4", "claude-3"]}', -- 权限范围限制，JSONB格式
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 4.2 修改角色权限
UPDATE a_base_role_permission
SET scope = '{"models": ["gpt-4", "claude-3", "gemini-pro"]}',
    valid_to = NOW() + INTERVAL '1 year',
    update_by = 'admin',
    update_time = NOW()
WHERE role_id = 1 AND permission_id = 1
  AND delete_time IS NULL;

-- 4.3 删除角色权限（软删除）
UPDATE a_base_role_permission
SET delete_time = NOW(),
    update_by = 'admin',
    update_time = NOW()
WHERE role_id = 1 AND permission_id = 1
  AND delete_time IS NULL;

-- 4.4 批量为角色分配权限
INSERT INTO a_base_role_permission (role_id, permission_id, create_by, update_by)
SELECT 1, id, 'admin', 'admin'
FROM a_base_permission
WHERE module = 'model'
  AND delete_time IS NULL
  AND id NOT IN (
    SELECT permission_id FROM a_base_role_permission 
    WHERE role_id = 1 AND delete_time IS NULL
  );

-- =====================================================
-- 5. 会员-角色映射管理 (a_member_role)
-- =====================================================

-- 5.1 为会员分配角色
INSERT INTO a_member_role (
    member_id, role_id, create_by, update_by
) VALUES (
    1, -- 会员ID
    1, -- 角色ID
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 5.2 删除会员角色（软删除）
UPDATE a_member_role
SET delete_time = NOW(),
    update_by = 'admin',
    update_time = NOW()
WHERE member_id = 1 AND role_id = 1
  AND delete_time IS NULL;

-- 5.3 查询会员的所有角色
SELECT r.id, r.code, r.name, r.description, mr.create_time as assigned_time
FROM a_member_role mr
JOIN a_base_role r ON mr.role_id = r.id AND r.delete_time IS NULL
WHERE mr.member_id = 1
  AND mr.delete_time IS NULL;

-- 5.4 批量为会员分配角色
INSERT INTO a_member_role (member_id, role_id, create_by, update_by)
SELECT 1, id, 'admin', 'admin'
FROM a_base_role
WHERE code IN ('user_basic', 'user_vip')
  AND delete_time IS NULL
  AND id NOT IN (
    SELECT role_id FROM a_member_role 
    WHERE member_id = 1 AND delete_time IS NULL
  );

-- =====================================================
-- 6. 会员-权限映射管理 (a_member_permission)
-- =====================================================

-- 6.1 为会员直接授予权限
INSERT INTO a_member_permission (
    member_id, permission_id, is_granted, scope, valid_from, valid_to,
    create_by, update_by
) VALUES (
    1, -- 会员ID
    1, -- 权限ID
    TRUE, -- TRUE=授予, FALSE=明确拒绝
    '{"models": ["gpt-4-turbo"]}', -- 权限范围限制
    NOW(), -- 权限生效时间
    NOW() + INTERVAL '30 days', -- 权限失效时间
    'admin', -- 创建人
    'admin' -- 修改人
);

-- 6.2 修改会员权限
UPDATE a_member_permission
SET is_granted = TRUE,
    scope = '{"models": ["gpt-4-turbo", "claude-3-opus"]}',
    valid_to = NOW() + INTERVAL '60 days',
    update_by = 'admin',
    update_time = NOW()
WHERE member_id = 1 AND permission_id = 1
  AND delete_time IS NULL;

-- 6.3 删除会员权限（软删除）
UPDATE a_member_permission
SET delete_time = NOW(),
    update_by = 'admin',
    update_time = NOW()
WHERE member_id = 1 AND permission_id = 1
  AND delete_time IS NULL;

-- 6.4 查询会员的直接权限
SELECT p.id, p.code, p.name, p.description,
       mp.is_granted, mp.scope, mp.valid_from, mp.valid_to
FROM a_member_permission mp
JOIN a_base_permission p ON mp.permission_id = p.id AND p.delete_time IS NULL
WHERE mp.member_id = 1
  AND mp.delete_time IS NULL;

-- =====================================================
-- 7. 权限判断逻辑
-- =====================================================

-- 7.1 判断会员是否拥有指定权限（包含角色继承和时效性检查）
CREATE OR REPLACE FUNCTION has_permission(
    p_member_id BIGINT,
    p_permission_code VARCHAR(100),
    p_scope JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_has_permission BOOLEAN := FALSE;
    v_permission_id BIGINT;
    v_now TIMESTAMPTZ := NOW();
    v_member_type SMALLINT;
    v_is_admin BOOLEAN := FALSE;
BEGIN
    -- 获取权限ID
    SELECT id INTO v_permission_id 
    FROM a_base_permission 
    WHERE code = p_permission_code AND delete_time IS NULL;
    
    IF v_permission_id IS NULL THEN
        RETURN FALSE; -- 权限不存在
    END IF;
    
    -- 检查会员是否为管理员
    SELECT member_type INTO v_member_type
    FROM a_member
    WHERE id = p_member_id AND delete_time IS NULL;
    
    IF v_member_type = 2 THEN -- 假设2为管理员类型
        RETURN TRUE; -- 管理员拥有所有权限
    END IF;
    
    -- 检查会员直接权限（优先级最高）
    SELECT is_granted INTO v_has_permission
    FROM a_member_permission
    WHERE member_id = p_member_id 
      AND permission_id = v_permission_id
      AND delete_time IS NULL
      AND (valid_from IS NULL OR valid_from <= v_now)
      AND (valid_to IS NULL OR valid_to >= v_now)
      -- 如果提供了scope参数，检查权限范围是否匹配
      AND (p_scope IS NULL OR 
           scope IS NULL OR 
           scope @> p_scope OR 
           p_scope @> scope)
    LIMIT 1;
    
    IF v_has_permission IS NOT NULL THEN
        RETURN v_has_permission; -- 直接返回会员显式的权限设置
    END IF;
    
    -- 检查会员通过角色获得的权限
    WITH RECURSIVE role_hierarchy AS (
        -- 获取会员直接拥有的角色
        SELECT r.id, r.parent_role_id
        FROM a_member_role mr
        JOIN a_base_role r ON mr.role_id = r.id
        WHERE mr.member_id = p_member_id
          AND mr.delete_time IS NULL
          AND r.delete_time IS NULL
        
        UNION
        
        -- 递归获取父角色
        SELECT r.id, r.parent_role_id
        FROM a_base_role r
        JOIN role_hierarchy rh ON r.id = rh.parent_role_id
        WHERE r.delete_time IS NULL
    )
    SELECT TRUE INTO v_has_permission
    FROM role_hierarchy rh
    JOIN a_base_role_permission rp ON rh.id = rp.role_id
    WHERE rp.permission_id = v_permission_id
      AND rp.delete_time IS NULL
      AND (rp.valid_from IS NULL OR rp.valid_from <= v_now)
      AND (rp.valid_to IS NULL OR rp.valid_to >= v_now)
      -- 如果提供了scope参数，检查权限范围是否匹配
      AND (p_scope IS NULL OR 
           rp.scope IS NULL OR 
           rp.scope @> p_scope OR 
           p_scope @> rp.scope)
    LIMIT 1;
    
    RETURN COALESCE(v_has_permission, FALSE);
END;
$$ LANGUAGE plpgsql;

-- 7.2 使用示例：检查用户是否有使用特定模型的权限
SELECT has_permission(
    1, -- 会员ID
    'model.chat.use', -- 权限代码
    '{"models": ["gpt-4"]}' -- 权限范围
);

-- 7.3 获取会员所有有效权限（包括从角色继承的权限）
CREATE OR REPLACE FUNCTION get_member_permissions(p_member_id BIGINT)
RETURNS TABLE (
    permission_id BIGINT,
    permission_code VARCHAR(100),
    permission_name VARCHAR(100),
    source_type VARCHAR(20), -- 'direct', 'role', 'inherited_role'
    source_id BIGINT,
    source_name VARCHAR(100),
    scope JSONB,
    valid_from TIMESTAMPTZ,
    valid_to TIMESTAMPTZ
) AS $$
DECLARE
    v_now TIMESTAMPTZ := NOW();
BEGIN
    -- 直接分配给会员的权限
    RETURN QUERY
    SELECT 
        p.id AS permission_id,
        p.code AS permission_code,
        p.name AS permission_name,
        'direct'::VARCHAR AS source_type,
        mp.member_id AS source_id,
        m.username AS source_name,
        mp.scope,
        mp.valid_from,
        mp.valid_to
    FROM a_member_permission mp
    JOIN a_base_permission p ON mp.permission_id = p.id AND p.delete_time IS NULL
    JOIN a_member m ON mp.member_id = m.id AND m.delete_time IS NULL
    WHERE mp.member_id = p_member_id
      AND mp.is_granted = TRUE
      AND mp.delete_time IS NULL
      AND (mp.valid_from IS NULL OR mp.valid_from <= v_now)
      AND (mp.valid_to IS NULL OR mp.valid_to >= v_now);

    -- 通过角色获得的权限
    RETURN QUERY
    WITH RECURSIVE role_hierarchy AS (
        -- 获取会员直接拥有的角色
        SELECT r.id, r.code, r.name, r.parent_role_id, 'role'::VARCHAR AS role_type
        FROM a_member_role mr
        JOIN a_base_role r ON mr.role_id = r.id
        WHERE mr.member_id = p_member_id
          AND mr.delete_time IS NULL
          AND r.delete_time IS NULL
        
        UNION
        
        -- 递归获取父角色
        SELECT r.id, r.code, r.name, r.parent_role_id, 'inherited_role'::VARCHAR AS role_type
        FROM a_base_role r
        JOIN role_hierarchy rh ON r.id = rh.parent_role_id
        WHERE r.delete_time IS NULL
    )
    SELECT 
        p.id AS permission_id,
        p.code AS permission_code,
        p.name AS permission_name,
        rh.role_type AS source_type,
        rh.id AS source_id,
        rh.name AS source_name,
        rp.scope,
        rp.valid_from,
        rp.valid_to
    FROM role_hierarchy rh
    JOIN a_base_role_permission rp ON rh.id = rp.role_id
    JOIN a_base_permission p ON rp.permission_id = p.id AND p.delete_time IS NULL
    WHERE rp.delete_time IS NULL
      AND (rp.valid_from IS NULL OR rp.valid_from <= v_now)
      AND (rp.valid_to IS NULL OR rp.valid_to >= v_now)
    ORDER BY permission_code, source_type;
END;
$$ LANGUAGE plpgsql;

-- 7.4 使用示例：获取会员所有权限
SELECT * FROM get_member_permissions(1);

-- 7.5 检查权限依赖关系
CREATE OR REPLACE FUNCTION check_permission_dependencies(
    p_member_id BIGINT,
    p_permission_code VARCHAR(100)
) RETURNS TABLE (
    permission_code VARCHAR(100),
    permission_name VARCHAR(100),
    is_granted BOOLEAN,
    is_required BOOLEAN,
    missing_dependency BOOLEAN
) AS $$
DECLARE
    v_permission_id BIGINT;
BEGIN
    -- 获取权限ID
    SELECT id INTO v_permission_id 
    FROM a_base_permission 
    WHERE code = p_permission_code AND delete_time IS NULL;
    
    IF v_permission_id IS NULL THEN
        RETURN; -- 权限不存在
    END IF;
    
    RETURN QUERY
    WITH dependencies AS (
        SELECT 
            p.code AS permission_code,
            p.name AS permission_name,
            pd.is_required,
            has_permission(p_member_id, p.code) AS is_granted
        FROM a_base_permission_dependency pd
        JOIN a_base_permission p ON pd.dependency_id = p.id AND p.delete_time IS NULL
        WHERE pd.permission_id = v_permission_id
          AND pd.delete_time IS NULL
    )
    SELECT 
        d.permission_code,
        d.permission_name,
        d.is_granted,
        d.is_required,
        (d.is_required AND NOT d.is_granted) AS missing_dependency
    FROM dependencies d;
END;
$$ LANGUAGE plpgsql;

-- 7.6 使用示例：检查权限依赖关系
SELECT * FROM check_permission_dependencies(1, 'model.chat.use');