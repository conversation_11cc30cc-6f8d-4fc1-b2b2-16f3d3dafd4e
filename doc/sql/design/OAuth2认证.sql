-- ====================================================================================
-- AIShowLab - OAuth2 认证授权模块数据库架构脚本 (PostgreSQL) v1.0
-- 作者: Gemini
-- 描述: 本脚本定义了支持标准 OAuth2 协议所需的核心表结构，
--      包括客户端详情、授权码、访问令牌和刷新令牌。
--      该设计参考了 Spring Security OAuth2 的标准实现，并适配本项目。
-- ====================================================================================


-- ==================================================
-- 1. OAuth2 客户端详情表 (a_oauth_client_details)
-- ==================================================
CREATE TABLE a_oauth_client_details (
    id                      BIGSERIAL PRIMARY KEY,
    client_id               VARCHAR(256) NOT NULL UNIQUE,
    client_secret           TEXT,
    client_name             VARCHAR(255) NOT NULL,
    resource_ids            VARCHAR(256),
    scope                   VARCHAR(256),
    authorized_grant_types  VARCHAR(256) NOT NULL,
    web_server_redirect_uri VARCHAR(256),
    authorities             VARCHAR(256),
    access_token_validity   INTEGER,
    refresh_token_validity  INTEGER,
    additional_information  TEXT,
    autoapprove             VARCHAR(256),
    owner_member_id           BIGINT,
    delete_time             TIMESTAMPTZ,
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_client_owner_member FOREIGN KEY (owner_member_id) REFERENCES a_member(id) ON DELETE SET NULL
);

CREATE INDEX idx_client_owner_member_id ON a_oauth_client_details(owner_member_id);

COMMENT ON TABLE a_oauth_client_details IS 'OAuth2客户端详情表，用于存储第三方应用或第一方服务作为客户端接入时的认证信息和授权规则。';
COMMENT ON COLUMN a_oauth_client_details.id IS '自增主键';
COMMENT ON COLUMN a_oauth_client_details.client_id IS '客户端唯一ID';
COMMENT ON COLUMN a_oauth_client_details.client_secret IS '客户端密钥（应加密存储）';
COMMENT ON COLUMN a_oauth_client_details.client_name IS '客户端名称，用于显示';
COMMENT ON COLUMN a_oauth_client_details.resource_ids IS '客户端可访问的资源ID集合，逗号分隔';
COMMENT ON COLUMN a_oauth_client_details.scope IS '客户端的权限范围，逗号分隔，如: read, write';
COMMENT ON COLUMN a_oauth_client_details.authorized_grant_types IS '授权类型，逗号分隔，如: authorization_code,password,refresh_token,client_credentials';
COMMENT ON COLUMN a_oauth_client_details.web_server_redirect_uri IS '授权码模式下的回调地址';
COMMENT ON COLUMN a_oauth_client_details.authorities IS '授予客户端的权限，逗号分隔';
COMMENT ON COLUMN a_oauth_client_details.access_token_validity IS '访问令牌的有效期（秒）';
COMMENT ON COLUMN a_oauth_client_details.refresh_token_validity IS '刷新令牌的有效期（秒）';
COMMENT ON COLUMN a_oauth_client_details.additional_information IS '附加信息，以JSON格式存储';
COMMENT ON COLUMN a_oauth_client_details.autoapprove IS '对于某些scope是否自动授权，逗号分隔';
COMMENT ON COLUMN a_oauth_client_details.owner_member_id IS '此客户端的所有者会员ID，用于管理';
COMMENT ON COLUMN a_oauth_client_details.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_oauth_client_details.create_time IS '创建时间';
COMMENT ON COLUMN a_oauth_client_details.update_time IS '更新时间';


-- ==================================================
-- 2. OAuth2 访问令牌表 (a_oauth_access_token)
-- ==================================================
CREATE TABLE a_oauth_access_token (
    id                      BIGSERIAL PRIMARY KEY,
    token_id                VARCHAR(256),
    token                   BYTEA,
    authentication_id       VARCHAR(256) NOT NULL UNIQUE,
    member_name               VARCHAR(256),
    client_id               VARCHAR(256),
    authentication          BYTEA,
    refresh_token           VARCHAR(256),
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE a_oauth_access_token IS 'OAuth2访问令牌存储表。存储序列化后的AccessToken对象。';
COMMENT ON COLUMN a_oauth_access_token.id IS '自增主键';
COMMENT ON COLUMN a_oauth_access_token.token_id IS '令牌ID (通常是MD5(token))';
COMMENT ON COLUMN a_oauth_access_token.token IS '序列化后的 AccessToken 对象';
COMMENT ON COLUMN a_oauth_access_token.authentication_id IS '认证信息ID (通常是MD5(authentication))';
COMMENT ON COLUMN a_oauth_access_token.member_name IS '会员名';
COMMENT ON COLUMN a_oauth_access_token.client_id IS '客户端ID';
COMMENT ON COLUMN a_oauth_access_token.authentication IS '序列化后的 Authentication 对象';
COMMENT ON COLUMN a_oauth_access_token.refresh_token IS '关联的刷新令牌ID';
COMMENT ON COLUMN a_oauth_access_token.create_time IS '创建时间';
COMMENT ON COLUMN a_oauth_access_token.update_time IS '更新时间';


-- ==================================================
-- 3. OAuth2 刷新令牌表 (a_oauth_refresh_token)
-- ==================================================
CREATE TABLE a_oauth_refresh_token (
    id                      BIGSERIAL PRIMARY KEY,
    token_id                VARCHAR(256),
    token                   BYTEA,
    authentication          BYTEA,
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE a_oauth_refresh_token IS 'OAuth2刷新令牌存储表。存储序列化后的RefreshToken对象。';
COMMENT ON COLUMN a_oauth_refresh_token.id IS '自增主键';
COMMENT ON COLUMN a_oauth_refresh_token.token_id IS '令牌ID (通常是MD5(token))';
COMMENT ON COLUMN a_oauth_refresh_token.token IS '序列化后的 RefreshToken 对象';
COMMENT ON COLUMN a_oauth_refresh_token.authentication IS '序列化后的 Authentication 对象';
COMMENT ON COLUMN a_oauth_refresh_token.create_time IS '创建时间';
COMMENT ON COLUMN a_oauth_refresh_token.update_time IS '更新时间';


-- ==================================================
-- 4. OAuth2 授权码表 (a_oauth_code)
-- ==================================================
CREATE TABLE a_oauth_code (
    id                      BIGSERIAL PRIMARY KEY,
    code                    VARCHAR(256),
    authentication          BYTEA,
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE a_oauth_code IS 'OAuth2授权码存储表，用于授权码模式。';
COMMENT ON COLUMN a_oauth_code.id IS '自增主键';
COMMENT ON COLUMN a_oauth_code.code IS '授权码';
COMMENT ON COLUMN a_oauth_code.authentication IS '序列化后的 Authentication 对象';
COMMENT ON COLUMN a_oauth_code.create_time IS '创建时间';
COMMENT ON COLUMN a_oauth_code.update_time IS '更新时间';


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 a_oauth_client_details 表绑定触发器
CREATE TRIGGER set_oauth_client_details_update_time
BEFORE UPDATE ON a_oauth_client_details
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


CREATE TRIGGER set_oauth_access_token_update_time
BEFORE UPDATE ON a_oauth_access_token
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


CREATE TRIGGER set_oauth_refresh_token_update_time
BEFORE UPDATE ON a_oauth_refresh_token
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


CREATE TRIGGER set_oauth_code_update_time
BEFORE UPDATE ON a_oauth_code
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- ==================================================
-- 脚本结束
-- ================================================== 