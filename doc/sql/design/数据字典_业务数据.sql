-- ====================================================================================
-- AIShowLab - 业务数据字典
-- 作者: Gemini
-- 描述: 本脚本用于向 sys_dict_type 和 sys_dict_data 表中插入业务所需的字典数据。
--      这些数据将替代原有的 ENUM 类型，以提高系统的灵活性和可维护性。
--      v2.0: 全面重构和修复由工具错误导致的文件损坏。
--      v3.0: 将所有 dict_value 从字符串改为数字，以优化性能。
-- ====================================================================================


-- ==================================================
-- 1. 用户与权限模块
-- ==================================================

-- 字典类型: member_type (会员类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('会员类型', 'member_type', '0', '定义会员的基本分类');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '普通会员', '1', 'member_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '管理员', '2', 'member_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '访客', '3', 'member_type', 'N', '0');

-- 字典类型: member_status (会员状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('会员状态', 'member_status', '0', '定义会员的账户状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '正常', '1', 'member_status', 'success','Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '禁用', '2', 'member_status', 'danger', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '待激活', '3', 'member_status', 'warning','N', '0');

-- 字典类型: auth_type (认证方式)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('认证方式', 'auth_type', '0', '定义会员登录认证的各种方式');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '密码', '1', 'auth_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '手机', '2', 'auth_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '邮箱', '3', 'auth_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '谷歌', '4', 'auth_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (5, 'GitHub', '5', 'auth_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (6, '微信', '6', 'auth_type', 'N', '0');

-- 字典类型: subject_type (权限主体类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('权限主体类型', 'subject_type', '0', '定义权限可以赋予的对象类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '会员', '1', 'subject_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '角色', '2', 'subject_type', 'N', '0');

-- 字典类型: member_level (会员计费等级)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('会员计费等级', 'member_level', '0', '定义会员的不同计费或服务等级');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '免费版', '1', 'member_level', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, 'VIP版', '2', 'member_level', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '企业版', '3', 'member_level', 'N', '0');


-- ==================================================
-- 2. 模型管理模块
-- ==================================================

-- 字典类型: model_source (模型来源)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('模型来源', 'model_source', '0', '区分模型是内部部署还是第三方服务');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '第三方', '1', 'model_source', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '内部模型', '2', 'model_source', 'N', '0');

-- 字典类型: model_invoke_method (模型调用方式)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('模型调用方式', 'model_invoke_method', '0', '定义了调用AI模型的不同技术方式');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, 'HTTP', '1', 'model_invoke_method', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, 'WebSocket', '2', 'model_invoke_method', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, 'SDK', '3', 'model_invoke_method', 'N', '0');

-- 字典类型: model_visibility_type (模型可见性类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('模型可见性类型', 'model_visibility_type', '0', '定义了模型可见性规则的作用维度');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '按角色', '1', 'model_visibility_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '按会员', '2', 'model_visibility_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '按地区', '3', 'model_visibility_type', 'N', '0');

-- 字典类型: model_output_type (模型输出类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('模型输出类型', 'model_output_type', '0', '定义模型支持的输出格式类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, 'JSON', '1', 'model_output_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '文本', '2', 'model_output_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '图片', '3', 'model_output_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '音频', '4', 'model_output_type', 'N', '0');

-- 字典类型: model_call_status (模型调用状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('模型调用状态', 'model_call_status', '0', '定义模型调用的状态，用于统计分析和监控');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '成功', '1', 'model_call_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '失败', '2', 'model_call_status', 'danger', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '处理中', '3', 'model_call_status', 'warning', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (4, '超时', '4', 'model_call_status', 'danger', 'N', '0');

-- ==================================================
-- 3. 模型调用与日志模块
-- ==================================================

-- 字典类型: request_status (请求状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('请求状态', 'request_status', '0', '定义了模型调用请求的生命周期状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '成功', '1', 'request_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '失败', '2', 'request_status', 'danger', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '处理中', '3', 'request_status', 'warning', 'N', '0');

-- 字典类型: response_format (响应格式)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('响应格式', 'response_format', '0', '定义了模型调用响应的数据格式');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '纯文本', '1', 'response_format', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, 'JSON', '2', 'response_format', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '图片', '3', 'response_format', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '音频', '4', 'response_format', 'N', '0');

-- 字典类型: error_source (错误来源)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('错误来源', 'error_source', '0', '定义了错误日志中记录的错误来源');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '系统', '1', 'error_source', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '模型', '2', 'error_source', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '网络', '3', 'error_source', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '数据库', '4', 'error_source', 'N', '0');

-- 字典类型: audit_action (审计动作)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('审计动作', 'audit_action', '0', '定义了需要记录到审计日志中的关键操作类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, status) values (1, '会员登录', '1', 'audit_action', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, status) values (2, '会员登出', '2', 'audit_action', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, status) values (10, '角色创建', '10', 'audit_action', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, status) values (11, '权限授予', '11', 'audit_action', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, status) values (20, '余额充值', '20', 'audit_action', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, status) values (21, '余额消耗', '21', 'audit_action', '0');


-- ==================================================
-- 4. 计费与消耗模块
-- ==================================================

-- 字典类型: billing_unit (计费单位)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('计费单位', 'billing_unit', '0', '定义了服务消耗的计量单位');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, 'Token', '1', 'billing_unit', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '次', '2', 'billing_unit', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '张(图)', '3', 'billing_unit', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '秒(音频)', '4', 'billing_unit', 'N', '0');

-- 字典类型: transaction_type (交易类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('交易类型', 'transaction_type', '0', '记录了会员账户余额变动的类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '消费', '1', 'transaction_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '充值', '2', 'transaction_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '退款', '3', 'transaction_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '赠送', '4', 'transaction_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (5, '分润', '5', 'transaction_type', 'N', '0');

-- 字典类型: coupon_type (优惠券类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('优惠券类型', 'coupon_type', '0', '定义了不同种类的优惠券');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '固定金额', '1', 'coupon_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '百分比折扣', '2', 'coupon_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '满减', '3', 'coupon_type', 'N', '0');

-- 字典类型: coupon_status (优惠券状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('优惠券状态', 'coupon_status', '0', '定义了优惠券的生命周期状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '可用', '1', 'coupon_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '已用', '2', 'coupon_status', 'info', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '过期', '3', 'coupon_status', 'danger', 'N', '0');

-- 字典类型: package_type (产品包类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('产品包类型', 'package_type', '0', '产品包类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '一次性', '1', 'package_type', 'primary', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '订阅', '2', 'package_type', 'success', 'N', '0');

-- 字典类型: package_status (产品包状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('产品包状态', 'package_status', '0', '产品包状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '草稿', '1', 'package_status', 'info', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '上架', '2', 'package_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '下架', '3', 'package_status', 'danger', 'N', '0');

-- 字典类型: order_status (订单状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('订单状态', 'order_status', '0', '订单状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '待支付', '1', 'order_status', 'warning', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '已完成', '2', 'order_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '已取消', '3', 'order_status', 'info', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (4, '支付失败', '4', 'order_status', 'danger', 'N', '0');

-- 字典类型: redemption_type (兑换码类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('兑换码类型', 'redemption_type', '0', '兑换码类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '兑换点数', '1', 'redemption_type', 'primary', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '兑换套餐', '2', 'redemption_type', 'success', 'N', '0');

-- 字典类型: redemption_status (兑换码状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('兑换码状态', 'redemption_status', '0', '兑换码状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '有效', '1', 'redemption_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '失效', '2', 'redemption_status', 'danger', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '已用完', '3', 'redemption_status', 'warning', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (4, '已过期', '4', 'redemption_status', 'info', 'N', '0');


-- ==================================================
-- 5. 通用模块
-- ==================================================

-- 字典类型: continent (大洲)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('大洲', 'continent', '0', '全球七大洲');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '亚洲', '1', 'continent', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '欧洲', '2', 'continent', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '北美洲', '3', 'continent', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '南美洲', '4', 'continent', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (5, '非洲', '5', 'continent', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (6, '大洋洲', '6', 'continent', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (7, '南极洲', '7', 'continent', 'N', '0');

-- 字典类型: document_status (文档状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('文档状态', 'document_status', '0', '定义了静态内容文档的生命周期状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '草稿', '1', 'document_status', 'info', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '已发布', '2', 'document_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '归档', '3', 'document_status', 'warning', 'N', '0');


-- ==================================================
-- 6. AI 助手模块
-- ==================================================

-- 字典类型: assistant_interaction_mode (助手交互模式)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('助手交互模式', 'assistant_interaction_mode', '0', '定义了AI助手的核心工作方式和交互逻辑');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status, remark) values (1, '标准对话', '1', 'assistant_interaction_mode', 'Y', '0', '适用于标准问答、角色扮演等');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status, remark) values (2, '图像分析', '2', 'assistant_interaction_mode', 'N', '0', '适用于拍照识物、图像内容分析等场景');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status, remark) values (3, '文生视频', '3', 'assistant_interaction_mode', 'N', '0', '接收文本，处理后返回视频链接或文件');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status, remark) values (4, '持续监听', '4', 'assistant_interaction_mode', 'N', '0', '用于实现唤醒词记录等后台监听任务');

-- 字典类型: assistant_status (助手状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('助手状态', 'assistant_status', '0', '定义了AI助手在应用市场中的生命周期状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (1, '草稿', '1', 'assistant_status', 'info', 'N', '0', '助手正在创建和配置中，仅所有者可见');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (2, '待审核', '2', 'assistant_status', 'warning', 'N', '0', '所有者已提交发布申请，等待平台管理员审核');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (3, '已发布', '3', 'assistant_status', 'success', 'Y', '0', '审核通过，在应用市场对符合条件的用户可见');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (4, '已归档', '4', 'assistant_status', 'danger', 'N', '0', '助手已下架，不再可见，但保留历史数据');

-- 字典类型: param_type (助手参数类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('助手参数类型', 'param_type', '0', '定义了助手可配置参数在前端渲染的控件类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '文本输入', '1', 'param_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '数字输入', '2', 'param_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '开关', '3', 'param_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, '下拉选择', '4', 'param_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (5, '多行文本', '5', 'param_type', 'N', '0');


-- ==================================================
-- 7. 知识库模块
-- ==================================================
-- 字典类型: knowledge_permission_type (知识库权限类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('知识库权限类型', 'knowledge_permission_type', '0', '定义知识库的访问权限级别');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '私有', '1', 'knowledge_permission_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '租户共享', '2', 'knowledge_permission_type', 'N', '0');

-- 字典类型: doc_processing_status (知识库文档处理状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('知识库文档处理状态', 'doc_processing_status', '0', '记录了知识库文档从上传到可用的完整生命周期状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (1, '上传中', '1', 'doc_processing_status', 'primary', 'N', '0', '文件正在上传到服务器');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (2, '待处理', '2', 'doc_processing_status', 'info', 'N', '0', '文件上传完成，等待后台任务处理');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (3, '切分中', '3', 'doc_processing_status', 'warning', 'N', '0', '正在对文档进行文本切块');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (4, '向量化', '4', 'doc_processing_status', 'warning', 'N', '0', '正在为文本块生成向量并存入向量数据库');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (5, '就绪', '5', 'doc_processing_status', 'success', 'Y', '0', '文档处理完成，可以在RAG中被检索');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, remark) values (6, '错误', '6', 'doc_processing_status', 'danger', 'N', '0', '处理过程中发生错误');


-- ==================================================
-- 8. 密钥管理模块
-- ==================================================

-- 字典类型: model_provider (模型供应商)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('模型供应商', 'model_provider', '0', '定义了系统集成的AI模型供应商');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, 'OpenAI', '1', 'model_provider', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, 'Anthropic', '2', 'model_provider', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, 'Google', '3', 'model_provider', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, 'Moonshot', '4', 'model_provider', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (5, 'DeepSeek', '5', 'model_provider', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (99, 'Internal', '99', 'model_provider', 'N', '0');

-- 字典类型: api_key_status (API密钥状态)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('API密钥状态', 'api_key_status', '0', '定义了API密钥的生命周期状态');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (1, '正常', '1', 'api_key_status', 'success', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (2, '禁用', '2', 'api_key_status', 'danger', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status) values (3, '额度耗尽', '3', 'api_key_status', 'warning', 'N', '0');

-- 字典类型: provider_channel_type (供应商渠道类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('供应商渠道类型', 'provider_channel_type', '0', '定义了模型供应商是官方渠道还是代理渠道');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '官方', '1', 'provider_channel_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '代理', '2', 'provider_channel_type', 'N', '0');


-- ==================================================
-- 9. 业务流程相关
-- ==================================================

-- 字典类型: user_session_device (用户会话设备)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('用户会话设备', 'user_session_device', '0', '记录用户登录时的设备类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, 'Web端', '1', 'user_session_device', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, 'iOS客户端', '2', 'user_session_device', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, 'Android客户端', '3', 'user_session_device', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (99, '未知', '99', 'user_session_device', 'N', '0');

-- 字典类型: file_mime_type (文件MIME类型)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('文件MIME类型', 'file_mime_type', '0', '知识库等模块支持的文件类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, 'PDF文档', '1', 'file_mime_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '纯文本', '2', 'file_mime_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, 'Markdown文档', '3', 'file_mime_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (4, 'Word文档 (docx)', '4', 'file_mime_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (5, 'Excel工作簿 (xlsx)', '5', 'file_mime_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (6, 'PowerPoint演示文稿 (pptx)', '6', 'file_mime_type', 'N', '0');

-- 字典类型: assistant_category_type (AI助手分类)
insert into sys_dict_type (dict_name, dict_type, status, remark) values ('AI助手分类', 'assistant_category_type', '0', 'AI助手的业务分类');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (1, '生活娱乐', '1', 'assistant_category_type', 'Y', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (2, '办公效率', '2', 'assistant_category_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (3, '创意趣味', '3', 'assistant_category_type', 'N', '0');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, is_default, status) values (99, '其他', '99', 'assistant_category_type', 'N', '0'); 