-- ====================================================================================
-- AIShowLab - 会员与权限模块数据库架构脚本 (PostgreSQL) v1.2
-- 作者: Gemini
-- 描述: 本脚本定义了会员、角色、权限等核心用户管理相关的表结构。
-- ====================================================================================

-- ==================================================
-- 会员主表 (a_member)
-- ==================================================
CREATE TABLE a_member (
    id              BIGSERIAL PRIMARY KEY,
    member_type     SMALLINT DEFAULT 1 NOT NULL,
    member_level    SMALLINT DEFAULT 1 NOT NULL,
    username        VARCHAR(50),
    nickname        VARCHAR(50),
    password        VARCHAR(100),
    avatar          VARCHAR(255),
    gender          SMALLINT DEFAULT 0,
    email           VARCHAR(100),
    phone           VARCHAR(20),
    invitation_code VARCHAR(50),
    inviter_id      BIGINT,
    status          SMALLINT DEFAULT 1 NOT NULL,
    country_id      BIGINT,
    lang_id         BIGINT,
    time_zone       SMALLINT DEFAULT 1 NOT NULL,
    last_login_ip   VARCHAR(50),
    last_login_time TIMESTAMPTZ,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT username_not_empty CHECK (trim(username) <> ''),
    CONSTRAINT fk_member_inviter FOREIGN KEY (inviter_id) REFERENCES a_member(id) ON DELETE SET NULL
);
COMMENT ON TABLE a_member IS '会员主表，存储了系统的核心会员信息。';
COMMENT ON COLUMN a_member.id IS '主键ID';
COMMENT ON COLUMN a_member.member_type IS '会员类型 (字典: member_type), 1-普通会员, 2-管理员, 3-访客';
COMMENT ON COLUMN a_member.member_level IS '会员计费等级 (字典: member_level), 1-免费版, 2-VIP版, 3-企业版';
COMMENT ON COLUMN a_member.username IS '会员登录名，唯一';
COMMENT ON COLUMN a_member.nickname IS '会员昵称';
COMMENT ON COLUMN a_member.password IS '加密后的登录密码';
COMMENT ON COLUMN a_member.avatar IS '会员头像URL';
COMMENT ON COLUMN a_member.gender IS '性别 (0-未知, 1-男, 2-女)';
COMMENT ON COLUMN a_member.email IS '电子邮箱，唯一';
COMMENT ON COLUMN a_member.phone IS '手机号码，唯一';
COMMENT ON COLUMN a_member.invitation_code IS '用户的专属邀请码，唯一';
COMMENT ON COLUMN a_member.inviter_id IS '邀请该用户的会员ID，关联 a_member.id';
COMMENT ON COLUMN a_member.status IS '会员状态 (字典: member_status), 1-正常, 2-禁用, 3-待激活';
COMMENT ON COLUMN a_member.country_id IS '国家/地区ID，关联 a_base_country.id';
COMMENT ON COLUMN a_member.lang_id IS '偏好语言ID，关联 a_base_lang.id';
COMMENT ON COLUMN a_member.time_zone IS '时区(字典: time_zone) (如: 1-America/New_York)';
COMMENT ON COLUMN a_member.last_login_ip IS '最后登录IP地址';
COMMENT ON COLUMN a_member.last_login_time IS '最后登录时间';
COMMENT ON COLUMN a_member.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_member.create_time IS '创建时间';
COMMENT ON COLUMN a_member.update_time IS '更新时间';

-- 创建索引以优化查询
CREATE INDEX idx_member_status ON a_member(status);
CREATE INDEX idx_member_type ON a_member(member_type);
CREATE UNIQUE INDEX idx_member_invitation_code_unique ON a_member(invitation_code) WHERE delete_time IS NULL;

-- ==================================================
-- 会员认证信息表 (a_member_auth)
-- ==================================================
CREATE TABLE a_member_auth (
    id              BIGSERIAL PRIMARY KEY,
    member_id       BIGINT NOT NULL,
    auth_type       SMALLINT NOT NULL,
    identifier      VARCHAR(255) NOT NULL,
    credential      VARCHAR(255),
    is_verified     BOOLEAN DEFAULT FALSE,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_auth_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_auth_type_identifier_unique ON a_member_auth(auth_type, identifier) WHERE delete_time IS NULL;
CREATE INDEX idx_auth_member_id ON a_member_auth(member_id);

COMMENT ON TABLE a_member_auth IS '会员认证信息表，用于支持多种登录方式（如密码、邮箱、手机、微信、Github等）。';
COMMENT ON COLUMN a_member_auth.id IS '主键ID';
COMMENT ON COLUMN a_member_auth.member_id IS '外键，关联 a_member.id';
COMMENT ON COLUMN a_member_auth.auth_type IS '认证方式 (字典: auth_type), 如 1-密码, 2-手机, 3-邮箱, 4-微信等';
COMMENT ON COLUMN a_member_auth.identifier IS '认证唯一标识 (如用户名、手机号、邮箱地址、三方应用的openId)';
COMMENT ON COLUMN a_member_auth.credential IS '凭证 (如密码、三方应用的access_token)';
COMMENT ON COLUMN a_member_auth.is_verified IS '该认证方式是否已验证（如邮箱是否已确认）';
COMMENT ON COLUMN a_member_auth.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_member_auth.create_time IS '创建时间';
COMMENT ON COLUMN a_member_auth.update_time IS '更新时间';


-- ==================================================
-- 会话信息表 (a_member_session)
-- ==================================================
CREATE TABLE a_member_session (
    id              BIGSERIAL PRIMARY KEY,
    member_id       BIGINT NOT NULL,
    token           VARCHAR(255) NOT NULL,
    device_type     SMALLINT,
    device_info     VARCHAR(255),
    ip_address      VARCHAR(50),
    login_time      TIMESTAMPTZ DEFAULT NOW(),
    expire_time     TIMESTAMPTZ,
    is_active       BOOLEAN DEFAULT TRUE,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_session_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX idx_session_token_unique ON a_member_session(token);
CREATE INDEX idx_session_member_id ON a_member_session(member_id);
CREATE INDEX idx_session_expire_time ON a_member_session(expire_time);

COMMENT ON TABLE a_member_session IS '会员会话信息表，用于管理和追踪会员的登录状态和会话。';
COMMENT ON COLUMN a_member_session.id IS '主键ID';
COMMENT ON COLUMN a_member_session.member_id IS '外键，关联 a_member.id';
COMMENT ON COLUMN a_member_session.token IS '会话令牌 (如 JWT)';
COMMENT ON COLUMN a_member_session.device_type IS '设备类型 (字典: member_device)';
COMMENT ON COLUMN a_member_session.device_info IS '设备详细信息 (如: "iPhone 13 Pro Max", "Chrome 108 on Windows 11")';
COMMENT ON COLUMN a_member_session.ip_address IS '登录IP地址';
COMMENT ON COLUMN a_member_session.login_time IS '登录时间';
COMMENT ON COLUMN a_member_session.expire_time IS '会话过期时间';
COMMENT ON COLUMN a_member_session.is_active IS '是否在线';
COMMENT ON COLUMN a_member_session.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_member_session.create_time IS '创建时间';
COMMENT ON COLUMN a_member_session.update_time IS '更新时间';

-- 创建索引以优化查询
CREATE INDEX idx_session_member_online ON a_member_session(member_id, is_active) WHERE is_active = TRUE; -- 部分索引，高效查找会员的在线会话


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新时间的表绑定触发器
CREATE TRIGGER set_member_update_time
BEFORE UPDATE ON a_member
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_member_auth_update_time
BEFORE UPDATE ON a_member_auth
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_member_role_update_time
BEFORE UPDATE ON a_member_role
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_member_permission_update_time
BEFORE UPDATE ON a_member_permission
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_member_session_update_time
BEFORE UPDATE ON a_member_session
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- ==================================================
-- 脚本结束
-- ==================================================


-- ====================================================================================
-- 进阶优化建议 (可选，根据业务复杂性决定是否实施)
-- ====================================================================================
--
-- 1.  **分区表 (Partitioning)**:
--     对于 `a_member_session` 这类日志型或时序性很强的表，当数据量达到千万级时，可以按 `login_time` 进行范围分区。
--     - **优点**: 提高查询性能（特别是涉及时间范围的查询），便于数据归档和删除旧数据。
--     - **示例**:
--       ```sql
--       CREATE TABLE a_member_session (
--           -- ... columns ...
--       ) PARTITION BY RANGE (login_time);
--
--       -- 创建每月的分区
--       CREATE TABLE a_member_session_2024_01 PARTITION OF a_member_session
--           FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
--       CREATE TABLE a_member_session_2024_02 PARTITION OF a_member_session
--           FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
--       -- ... etc.
--       ```
--
-- 2.  **更细粒度的权限控制**:
--     如果需要实现更复杂的权限逻辑，例如“某个会员只能访问自己创建的数据”，则需要在查询中加入额外的 `WHERE` 条件，或者设计更复杂的 `scope` 结构。
--     - **行级安全策略 (Row-Level Security, RLS)**: PostgreSQL 提供了强大的RLS功能，可以为表定义安全策略，自动过滤数据行。
--     - **示例**: 假设要限制会员只能看到自己的会话记录。
--       ```sql
--       -- 启用RLS
--       ALTER TABLE a_member_session ENABLE ROW LEVEL SECURITY;
--
--       -- 创建策略
--       CREATE POLICY member_session_policy ON a_member_session
--           FOR SELECT
--           USING (member_id = current_setting('app.current_member_id')::BIGINT);
--       -- `current_setting('app.current_member_id')` 需要在应用层设置当前会话的会员ID
--       ```
--
-- 3.  **JSONB 索引**:
--     如果 `a_member` 表的 `settings` 字段查询频繁，可以为其内部的特定键创建索引。
--     - **GIN 索引**:
--       ```sql
--       -- 为所有键值对创建索引
--       CREATE INDEX idx_member_settings_gin ON a_member USING GIN (settings);
--
--       -- 为特定键创建索引
--       CREATE INDEX idx_member_settings_default_model ON a_member USING GIN ((settings -> 'default_model'));
--       ```
--
-- 4.  **逻辑复制 (Logical Replication)**:
--     可用于创建只读副本、数据集成或跨版本升级。
--
-- 5.  **安全加固**:
--     - **密码策略**: 在应用层强制执行复杂的密码策略（长度、字符集等）。
--     - **速率限制**: 对登录、注册等接口进行速率限制，防止暴力破解。
--     - **敏感数据加密**: `credential` 字段虽然存储的是哈希值，但对于其他潜在的敏感信息（如第三方token），应考虑在应用层加密后再存入数据库。
--
-- ==================================================================================== 