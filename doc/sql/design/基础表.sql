-- ====================================================================================
-- AIShowLab - 通用及其他模块数据库架构脚本 (PostgreSQL) v1.0
-- 作者: Gemini
-- 描述: 本脚本定义了不属于特定业务模块的通用表结构，如静态文档。
-- ====================================================================================


-- ==================================================
-- 1. 货币表 (a_base_currency)
-- ==================================================
CREATE TABLE a_base_currency (
    id              BIGSERIAL PRIMARY KEY,
    code            VARCHAR(10) NOT NULL,
    name            VARCHAR(50) NOT NULL,
    symbol          VARCHAR(10) NOT NULL,
    exchange_rate   NUMERIC(12, 6) DEFAULT 1.0 NOT NULL,
    is_default      BOOLEAN DEFAULT FALSE NOT NULL,
    sort_order      INTEGER DEFAULT 0 NOT NULL,
    is_enabled      BOOLEAN DEFAULT TRUE NOT NULL,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW()
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_currency_code_unique_active ON a_base_currency(code) WHERE delete_time IS NULL;
-- 确保最多只有一个未删除的默认货币
CREATE UNIQUE INDEX idx_currency_default_one_active ON a_base_currency(is_default) WHERE is_default = TRUE AND delete_time IS NULL;

COMMENT ON TABLE a_base_currency IS '货币表，用于定义系统中支持的所有交易货币。';
COMMENT ON COLUMN a_base_currency.id IS '主键ID';
COMMENT ON COLUMN a_base_currency.code IS '货币的ISO 4217代码 (例如: CNY, USD, EUR)';
COMMENT ON COLUMN a_base_currency.name IS '货币名称 (例如: 人民币, 美元)';
COMMENT ON COLUMN a_base_currency.symbol IS '货币符号 (例如: ¥, $, €)';
COMMENT ON COLUMN a_base_currency.exchange_rate IS '相对于系统基准货币的汇率';
COMMENT ON COLUMN a_base_currency.is_default IS '是否为系统默认货币';
COMMENT ON COLUMN a_base_currency.sort_order IS '排序值';
COMMENT ON COLUMN a_base_currency.is_enabled IS '是否启用该货币';
COMMENT ON COLUMN a_base_currency.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_base_currency.create_by IS '创建人';
COMMENT ON COLUMN a_base_currency.update_by IS '修改人';
COMMENT ON COLUMN a_base_currency.create_time IS '创建时间';
COMMENT ON COLUMN a_base_currency.update_time IS '更新时间';

-- ==================================================


-- ==================================================
-- 2. 语言表 (a_base_lang)
-- ==================================================
CREATE TABLE a_base_lang (
    id              BIGSERIAL PRIMARY KEY,
    code            VARCHAR(20) NOT NULL,
    name            VARCHAR(100) NOT NULL,
    native_name     VARCHAR(100) NOT NULL,
    country_code    VARCHAR(10) NOT NULL,
    icon_url        VARCHAR(255),
    is_default      BOOLEAN DEFAULT FALSE NOT NULL,
    sort_order      INTEGER DEFAULT 0 NOT NULL,
    is_enabled      BOOLEAN DEFAULT TRUE NOT NULL,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT lang_code_not_empty CHECK (trim(code) <> ''),
    CONSTRAINT lang_name_not_empty CHECK (trim(name) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_base_lang_code_unique_active ON a_base_lang(code) WHERE delete_time IS NULL;
-- 确保最多只有一个未删除的默认语言
CREATE UNIQUE INDEX idx_base_lang_default_one_active ON a_base_lang(is_default) WHERE is_default = TRUE AND delete_time IS NULL;
-- 常用查询索引
CREATE INDEX idx_base_lang_is_enabled ON a_base_lang(is_enabled);


COMMENT ON TABLE a_base_lang IS '语言表，用于定义系统中所有支持的语言选项，供用户界面、内容模板等多语言场景调用。';
COMMENT ON COLUMN a_base_lang.id IS '主键ID';
COMMENT ON COLUMN a_base_lang.code IS '标准语言代码，如 "en", "zh-CN", "ja"';
COMMENT ON COLUMN a_base_lang.name IS '显示名称，如 "English"';
COMMENT ON COLUMN a_base_lang.native_name IS '本地名称，如 "English", "中文", "日本語"';
COMMENT ON COLUMN a_base_lang.country_code IS '国家代码（可用于标识国旗图标等），如 "US", "CN", "JP"';
COMMENT ON COLUMN a_base_lang.icon_url IS '可选：图标路径，如国旗';
COMMENT ON COLUMN a_base_lang.is_default IS '是否为默认语言';
COMMENT ON COLUMN a_base_lang.sort_order IS '排序字段（前端展示用）';
COMMENT ON COLUMN a_base_lang.is_enabled IS '是否启用该语言';
COMMENT ON COLUMN a_base_lang.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_base_lang.create_by IS '创建人';
COMMENT ON COLUMN a_base_lang.update_by IS '修改人';
COMMENT ON COLUMN a_base_lang.create_time IS '创建时间';
COMMENT ON COLUMN a_base_lang.update_time IS '更新时间';



-- ==================================================
-- 3. 静态内容文档表 (a_base_document)
-- ==================================================
CREATE TABLE a_base_document (
    id              BIGSERIAL PRIMARY KEY,
    code            VARCHAR(50) NOT NULL,
    title           VARCHAR(255) NOT NULL,
    content         TEXT NOT NULL,
    summary         VARCHAR(500),
    lang_id         BIGINT,
    status          SMALLINT NOT NULL DEFAULT 1,
    sort_order      INTEGER DEFAULT 0,
    publish_time    TIMESTAMPTZ,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_document_lang FOREIGN KEY (lang_id) REFERENCES a_base_lang(id) ON DELETE SET NULL,
    CONSTRAINT document_code_not_empty CHECK (trim(code) <> ''),
    CONSTRAINT document_title_not_empty CHECK (trim(title) <> '')
);

-- 为软删除优化的唯一索引
-- 保证同一个标识(code)的每个语言版本是唯一的
CREATE UNIQUE INDEX idx_doc_code_lang_unique_active ON a_base_document(code, lang_id) WHERE delete_time IS NULL AND lang_id IS NOT NULL;
-- 保证同一个标识(code)的通用版本(lang_id IS NULL)是唯一的
CREATE UNIQUE INDEX idx_doc_code_null_lang_unique_active ON a_base_document(code) WHERE delete_time IS NULL AND lang_id IS NULL;

-- 常用查询索引
CREATE INDEX idx_document_status ON a_base_document(status);


COMMENT ON TABLE a_base_document IS '静态内容文档表，用于后台统一管理《会员协议》、《隐私政策》、联系我们等富文本内容。';
COMMENT ON COLUMN a_base_document.id IS '主键ID';
COMMENT ON COLUMN a_base_document.code IS '文档唯一标识 (如: privacy-policy, user-agreement)，程序调用使用';
COMMENT ON COLUMN a_base_document.title IS '文档标题';
COMMENT ON COLUMN a_base_document.content IS '文档正文 (支持HTML或Markdown)';
COMMENT ON COLUMN a_base_document.summary IS '文档摘要或简介';
COMMENT ON COLUMN a_base_document.lang_id IS '语言ID，关联 a_base_lang.id。NULL表示不特定语言的通用版本';
COMMENT ON COLUMN a_base_document.status IS '状态 (字典: document_status), 1-草稿, 2-已发布, 3-归档';
COMMENT ON COLUMN a_base_document.sort_order IS '排序值，用于在列表中调整顺序';
COMMENT ON COLUMN a_base_document.publish_time IS '发布时间';
COMMENT ON COLUMN a_base_document.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_base_document.create_by IS '创建人';
COMMENT ON COLUMN a_base_document.update_by IS '修改人';
COMMENT ON COLUMN a_base_document.create_time IS '创建时间';
COMMENT ON COLUMN a_base_document.update_time IS '更新时间';



-- ==================================================
-- 4. 国家地区表 (a_base_country)
-- ==================================================
CREATE TABLE a_base_country (
    id              BIGSERIAL PRIMARY KEY,
    name_en         VARCHAR(100) NOT NULL,
    name_native     VARCHAR(100) NOT NULL,
    iso2_code       VARCHAR(2) UNIQUE,
    iso3_code       VARCHAR(3) UNIQUE,
    numeric_code    VARCHAR(3) UNIQUE,
    phone_code      VARCHAR(20),
    capital         VARCHAR(100),
    continent       VARCHAR(50),
    flag_url        VARCHAR(255),
    is_enabled      BOOLEAN DEFAULT TRUE NOT NULL,
    sort_order      INTEGER DEFAULT 0 NOT NULL,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT country_name_en_not_empty CHECK (trim(name_en) <> '')
);

-- 创建索引
CREATE INDEX idx_base_country_iso2_code ON a_base_country(iso2_code);
CREATE INDEX idx_base_country_continent ON a_base_country(continent);

-- 添加注释
COMMENT ON TABLE a_base_country IS '国家地区表，用于存储全球国家和地区的基础数据，如ISO代码、电话区号等，为国际化功能提供支持。';
COMMENT ON COLUMN a_base_country.id IS '主键ID';
COMMENT ON COLUMN a_base_country.name_en IS '国家或地区的英文名称';
COMMENT ON COLUMN a_base_country.name_native IS '本地化名称';
COMMENT ON COLUMN a_base_country.iso2_code IS 'ISO 3166-1 alpha-2 两字母代码';
COMMENT ON COLUMN a_base_country.iso3_code IS 'ISO 3166-1 alpha-3 三字母代码';
COMMENT ON COLUMN a_base_country.numeric_code IS 'ISO 3166-1 numeric 三位数字代码';
COMMENT ON COLUMN a_base_country.phone_code IS '国际电话区号';
COMMENT ON COLUMN a_base_country.capital IS '首都';
COMMENT ON COLUMN a_base_country.continent IS '所在大洲 (字典: continent)';
COMMENT ON COLUMN a_base_country.flag_url IS '国旗图标URL';
COMMENT ON COLUMN a_base_country.is_enabled IS '是否启用';
COMMENT ON COLUMN a_base_country.sort_order IS '排序值';
COMMENT ON COLUMN a_base_country.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_base_country.create_by IS '创建人';
COMMENT ON COLUMN a_base_country.update_by IS '修改人';
COMMENT ON COLUMN a_base_country.create_time IS '创建时间';
COMMENT ON COLUMN a_base_country.update_time IS '更新时间';







-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
-- 确保函数存在。如果其他脚本已创建，则此命令无操作。
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 a_base_document 表绑定触发器
CREATE TRIGGER set_document_update_time
BEFORE UPDATE ON a_base_document
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- 为 a_base_currency 表绑定触发器
CREATE TRIGGER set_currency_update_time
BEFORE UPDATE ON a_base_currency
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- 为 a_base_lang 表绑定触发器
CREATE TRIGGER set_base_lang_update_time
BEFORE UPDATE ON a_base_lang
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- 为 a_base_country 表绑定触发器
CREATE TRIGGER set_country_update_time
BEFORE UPDATE ON a_base_country
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- ==================================================
-- 数据填充 (Data Seeding)
-- ==================================================
INSERT INTO a_base_currency (code, name, symbol, exchange_rate, is_default, sort_order) VALUES
('CNY', '人民币', '¥', 1.000000, TRUE, 1),
('USD', '美元', '$', 7.250000, FALSE, 2),
('EUR', '欧元', '€', 7.850000, FALSE, 3),
('JPY', '日元', '¥', 0.046000, FALSE, 4),
('GBP', '英镑', '£', 9.200000, FALSE, 5);


-- ==================================================
-- 数据填充 (Data Seeding)
-- ==================================================
COMMENT ON TABLE a_base_lang IS '语言表，用于定义系统中所有支持的语言选项，供用户界面、内容模板等多语言场景调用。数据基于 ISO 639-1 标准。';

-- 注意：这是一个较为全面的语言列表，您可以根据实际业务需求进行增补。
INSERT INTO a_base_lang (code, name, native_name, country_code, sort_order, is_default) VALUES
('en', 'English', 'English', 'US', 1, TRUE),
('zh-CN', 'Chinese (Simplified)', '中文（简体）', 'CN', 2, FALSE),
('es', 'Spanish', 'Español', 'ES', 3, FALSE),
('fr', 'French', 'Français', 'FR', 4, FALSE),
('de', 'German', 'Deutsch', 'DE', 5, FALSE),
('ja', 'Japanese', '日本語', 'JP', 6, FALSE),
('ko', 'Korean', '한국어', 'KR', 7, FALSE),
('ru', 'Russian', 'Русский', 'RU', 8, FALSE),
('pt', 'Portuguese', 'Português', 'PT', 9, FALSE),
('it', 'Italian', 'Italiano', 'IT', 10, FALSE),
('ar', 'Arabic', 'العربية', 'SA', 11, FALSE),
('hi', 'Hindi', 'हिन्दी', 'IN', 12, FALSE),
('nl', 'Dutch', 'Nederlands', 'NL', 13, FALSE),
('sv', 'Swedish', 'Svenska', 'SE', 14, FALSE),
('zh-TW', 'Chinese (Traditional)', '中文（繁體）', 'TW', 15, FALSE),
('af', 'Afrikaans', 'Afrikaans', 'ZA', 100, FALSE),
('bn', 'Bengali', 'বাংলা', 'BD', 100, FALSE),
('ca', 'Catalan', 'Català', 'ES', 100, FALSE),
('cs', 'Czech', 'Čeština', 'CZ', 100, FALSE),
('cy', 'Welsh', 'Cymraeg', 'GB', 100, FALSE),
('da', 'Danish', 'Dansk', 'DK', 100, FALSE),
('el', 'Greek', 'Ελληνικά', 'GR', 100, FALSE),
('fi', 'Finnish', 'Suomi', 'FI', 100, FALSE),
('he', 'Hebrew', 'עברית', 'IL', 100, FALSE),
('hu', 'Hungarian', 'Magyar', 'HU', 100, FALSE),
('id', 'Indonesian', 'Bahasa Indonesia', 'ID', 100, FALSE),
('ms', 'Malay', 'Bahasa Melayu', 'MY', 100, FALSE),
('no', 'Norwegian', 'Norsk', 'NO', 100, FALSE),
('pl', 'Polish', 'Polski', 'PL', 100, FALSE),
('ro', 'Romanian', 'Română', 'RO', 100, FALSE),
('sk', 'Slovak', 'Slovenčina', 'SK', 100, FALSE),
('th', 'Thai', 'ไทย', 'TH', 100, FALSE),
('tr', 'Turkish', 'Türkçe', 'TR', 100, FALSE),
('uk', 'Ukrainian', 'Українська', 'UA', 100, FALSE),
('vi', 'Vietnamese', 'Tiếng Việt', 'VN', 100, FALSE);


COMMENT ON TABLE a_base_country IS '国家地区表，用于存储全球国家和地区的基础数据，如ISO代码、电话区号等，为国际化功能提供支持。数据基于 ISO 3166 标准。';

-- 注意：这是一个较为全面的国家和地区列表，但可能仍未包含所有政治实体。
-- 您可以根据实际业务需求，调整或增补此列表。
INSERT INTO a_base_country (name_en, name_native, iso2_code, iso3_code, numeric_code, phone_code, capital, continent, sort_order) VALUES
('China', '中国', 'CN', 'CHN', '156', '+86', 'Beijing', 'asia', 1),
('United States', 'United States', 'US', 'USA', '840', '+1', 'Washington, D.C.', 'north_america', 2),
('Japan', '日本', 'JP', 'JPN', '392', '+81', 'Tokyo', 'asia', 3),
('United Kingdom', 'United Kingdom', 'GB', 'GBR', '826', '+44', 'London', 'europe', 4),
('France', 'France', 'FR', 'FRA', '250', '+33', 'Paris', 'europe', 5),
('Germany', 'Deutschland', 'DE', 'DEU', '276', '+49', 'Berlin', 'europe', 6),
('Russia', 'Россия', 'RU', 'RUS', '643', '+7', 'Moscow', 'europe', 7),
('Canada', 'Canada', 'CA', 'CAN', '124', '+1', 'Ottawa', 'north_america', 8),
('Australia', 'Australia', 'AU', 'AUS', '036', '+61', 'Canberra', 'oceania', 9),
('India', 'भारत', 'IN', 'IND', '356', '+91', 'New Delhi', 'asia', 10),
('Brazil', 'Brasil', 'BR', 'BRA', '076', '+55', 'Brasília', 'south_america', 11),
('South Korea', '대한민국', 'KR', 'KOR', '410', '+82', 'Seoul', 'asia', 12),
('Singapore', 'Singapore', 'SG', 'SGP', '702', '+65', 'Singapore', 'asia', 13),
('Italy', 'Italia', 'IT', 'ITA', '380', '+39', 'Rome', 'europe', 14),
('Spain', 'España', 'ES', 'ESP', '724', '+34', 'Madrid', 'europe', 15),
('Netherlands', 'Nederland', 'NL', 'NLD', '528', '+31', 'Amsterdam', 'europe', 16),
('Switzerland', 'Schweiz', 'CH', 'CHE', '756', '+41', 'Bern', 'europe', 17),
('Saudi Arabia', 'المملكة العربية السعودية', 'SA', 'SAU', '682', '+966', 'Riyadh', 'asia', 18),
('United Arab Emirates', 'الإمارات العربية المتحدة', 'AE', 'ARE', '784', '+971', 'Abu Dhabi', 'asia', 19),
('South Africa', 'South Africa', 'ZA', 'ZAF', '710', '+27', 'Pretoria', 'africa', 20),
('Afghanistan', 'افغانستان', 'AF', 'AFG', '004', '+93', 'Kabul', 'asia', 100),
('Argentina', 'Argentina', 'AR', 'ARG', '032', '+54', 'Buenos Aires', 'south_america', 100),
('Austria', 'Österreich', 'AT', 'AUT', '040', '+43', 'Vienna', 'europe', 100),
('Belgium', 'België', 'BE', 'BEL', '056', '+32', 'Brussels', 'europe', 100),
('Chile', 'Chile', 'CL', 'CHL', '152', '+56', 'Santiago', 'south_america', 100),
('Colombia', 'Colombia', 'CO', 'COL', '170', '+57', 'Bogotá', 'south_america', 100),
('Denmark', 'Danmark', 'DK', 'DNK', '208', '+45', 'Copenhagen', 'europe', 100),
('Egypt', 'مصر', 'EG', 'EGY', '818', '+20', 'Cairo', 'africa', 100),
('Finland', 'Suomi', 'FI', 'FIN', '246', '+358', 'Helsinki', 'europe', 100),
('Greece', 'Ελλάδα', 'GR', 'GRC', '300', '+30', 'Athens', 'europe', 100),
('Hong Kong', '香港', 'HK', 'HKG', '344', '+852', 'City of Victoria', 'asia', 100),
('Indonesia', 'Indonesia', 'ID', 'IDN', '360', '+62', 'Jakarta', 'asia', 100),
('Ireland', 'Éire', 'IE', 'IRL', '372', '+353', 'Dublin', 'europe', 100),
('Israel', 'ישראל', 'IL', 'ISR', '376', '+972', 'Jerusalem', 'asia', 100),
('Malaysia', 'Malaysia', 'MY', 'MYS', '458', '+60', 'Kuala Lumpur', 'asia', 100),
('Mexico', 'México', 'MX', 'MEX', '484', '+52', 'Mexico City', 'north_america', 100),
('New Zealand', 'New Zealand', 'NZ', 'NZL', '554', '+64', 'Wellington', 'oceania', 100),
('Nigeria', 'Nigeria', 'NG', 'NGA', '566', '+234', 'Abuja', 'africa', 100),
('Norway', 'Norge', 'NO', 'NOR', '578', '+47', 'Oslo', 'europe', 100),
('Pakistan', 'پاکستان', 'PK', 'PAK', '586', '+92', 'Islamabad', 'asia', 100),
('Peru', 'Perú', 'PE', 'PER', '604', '+51', 'Lima', 'south_america', 100),
('Philippines', 'Pilipinas', 'PH', 'PHL', '608', '+63', 'Manila', 'asia', 100),
('Poland', 'Polska', 'PL', 'POL', '616', '+48', 'Warsaw', 'europe', 100),
('Portugal', 'Portugal', 'PT', 'PRT', '620', '+351', 'Lisbon', 'europe', 100),
('Sweden', 'Sverige', 'SE', 'SWE', '752', '+46', 'Stockholm', 'europe', 100),
('Taiwan', '臺灣', 'TW', 'TWN', '158', '+886', 'Taipei', 'asia', 100),
('Thailand', 'ประเทศไทย', 'TH', 'THA', '764', '+66', 'Bangkok', 'asia', 100),
('Turkey', 'Türkiye', 'TR', 'TUR', '792', '+90', 'Ankara', 'asia', 100),
('Ukraine', 'Україна', 'UA', 'UKR', '804', '+380', 'Kyiv', 'europe', 100),
('Vietnam', 'Việt Nam', 'VN', 'VNM', '704', '+84', 'Hanoi', 'asia', 100);

-- ==================================================
-- 脚本结束
-- ================================================== 