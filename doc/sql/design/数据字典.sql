--注意：不要修改此文件。

CREATE TABLE public.sys_dict_type (
	dict_id bigserial NOT NULL,
	dict_name varchar(100) NULL,
	dict_type varchar(100) NULL,
	status bpchar(1) NULL,
	create_by varchar(64) NULL,
	create_time timestamp(6) NULL,
	update_by varchar(64) NULL,
	update_time timestamp(6) NULL,
	remark varchar(500) NULL,
	CONSTRAINT sys_dict_type_pkey PRIMARY KEY (dict_id)
);

CREATE TABLE public.sys_dict_data (
	dict_code bigserial NOT NULL,
	dict_sort int4 NULL,
	dict_label varchar(100) NULL,
	dict_value varchar(100) NULL,
	dict_type varchar(100) NULL,
	css_class varchar(100) NULL,
	list_class varchar(100) NULL,
	is_default bpchar(1) NULL,
	status bpchar(1) NULL,
	create_by varchar(64) NULL,
	create_time timestamp(6) NULL,
	update_by varchar(64) NULL,
	update_time timestamp(6) NULL,
	remark varchar(500) NULL,
	CONSTRAINT sys_dict_data_pkey PRIMARY KEY (dict_code)
);

