
-- ==================================================
-- 1. 兑换码模板表 (a_promote_redemption)
-- ==================================================
CREATE TABLE a_promote_redemption (
    id                  BIGSERIAL PRIMARY KEY,
    code                VARCHAR(50) NOT NULL,
    type                SMALLINT NOT NULL,
    value               NUMERIC(12, 4) NOT NULL,
    package_id_grant    BIGINT,
    total_quantity      INTEGER NOT NULL,
    used_quantity       INTEGER NOT NULL DEFAULT 0,
    per_user_limit      INTEGER NOT NULL DEFAULT 1,
    valid_from          TIMESTAMPTZ,
    valid_to            TIMESTAMPTZ,
    status              SMALLINT NOT NULL DEFAULT 1,
    description         TEXT,
    delete_time         TIMESTAMPTZ,
    create_by           VARCHAR(64),
    update_by           <PERSON><PERSON>HA<PERSON>(64),
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_redemption_package FOREIGN KEY (package_id_grant) REFERENCES a_billing_package(id) ON DELETE SET NULL
);

CREATE UNIQUE INDEX idx_redemption_code_unique ON a_promote_redemption(code) WHERE delete_time IS NULL;
CREATE INDEX idx_redemption_status ON a_promote_redemption(status);

COMMENT ON TABLE a_promote_redemption IS '兑换码模板表，用于定义营销活动中使用的兑换码的规则。';
COMMENT ON COLUMN a_promote_redemption.id IS '主键ID';
COMMENT ON COLUMN a_promote_redemption.code IS '兑换码，需唯一';
COMMENT ON COLUMN a_promote_redemption.type IS '兑换类型 (字典: redemption_type), 1-兑换点数, 2-兑换套餐';
COMMENT ON COLUMN a_promote_redemption.value IS '兑换的面值。如果类型是 credits，这里是点数数量。';
COMMENT ON COLUMN a_promote_redemption.package_id_grant IS '如果类型是 package，这里是 a_billing_package.id';
COMMENT ON COLUMN a_promote_redemption.total_quantity IS '该兑换码的总可用次数，NULL表示不限制';
COMMENT ON COLUMN a_promote_redemption.used_quantity IS '已用次数';
COMMENT ON COLUMN a_promote_redemption.per_user_limit IS '每个用户可使用的次数';
COMMENT ON COLUMN a_promote_redemption.valid_from IS '有效期开始时间';
COMMENT ON COLUMN a_promote_redemption.valid_to IS '有效期结束时间';
COMMENT ON COLUMN a_promote_redemption.status IS '兑换码状态 (字典: redemption_status), 1-有效, 2-失效, 3-已用完, 4-已过期';
COMMENT ON COLUMN a_promote_redemption.description IS '兑换码活动描述';
COMMENT ON COLUMN a_promote_redemption.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_promote_redemption.create_by IS '创建人';
COMMENT ON COLUMN a_promote_redemption.update_by IS '修改人';
COMMENT ON COLUMN a_promote_redemption.create_time IS '创建时间';
COMMENT ON COLUMN a_promote_redemption.update_time IS '更新时间';



-- ==================================================
-- 2. 用户兑换日志表 (a_promote_redemption_log)
-- ==================================================
CREATE TABLE a_promote_redemption_log (
    id              BIGSERIAL PRIMARY KEY,
    redemption_id   BIGINT NOT NULL,
    member_id         BIGINT NOT NULL,
    redeemed_time   TIMESTAMPTZ DEFAULT NOW(),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_redemption_log_redemption FOREIGN KEY (redemption_id) REFERENCES a_promote_redemption(id) ON DELETE CASCADE,
    CONSTRAINT fk_redemption_log_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE
);

CREATE INDEX idx_redemption_log_redemption_id ON a_promote_redemption_log(redemption_id);
CREATE INDEX idx_redemption_log_member_id ON a_promote_redemption_log(member_id);

COMMENT ON TABLE a_promote_redemption_log IS '记录哪个用户在何时使用了哪个兑换码。';
COMMENT ON COLUMN a_promote_redemption_log.id IS '主键ID';
COMMENT ON COLUMN a_promote_redemption_log.redemption_id IS '兑换码ID';
COMMENT ON COLUMN a_promote_redemption_log.member_id IS '使用该兑换码的会员ID';
COMMENT ON COLUMN a_promote_redemption_log.redeemed_time IS '兑换时间';
COMMENT ON COLUMN a_promote_redemption_log.create_time IS '创建时间';
COMMENT ON COLUMN a_promote_redemption_log.update_time IS '更新时间';


-- ==================================================
-- 3. 优惠券表 (a_promote_coupon)
-- ==================================================
CREATE TABLE a_promote_coupon (
    id              BIGSERIAL PRIMARY KEY,
    code            VARCHAR(50) NOT NULL,
    type            SMALLINT NOT NULL,
    status          SMALLINT DEFAULT 1,
    discount_value  NUMERIC(10, 4),
    threshold       NUMERIC(10, 4),
    valid_from      TIMESTAMPTZ,
    valid_to        TIMESTAMPTZ,
    applicable_model_ids BIGINT[],
    per_user_limit  INTEGER DEFAULT 1,
    total_quantity  INTEGER,
    used_quantity   INTEGER DEFAULT 0,
    bind_member_id    BIGINT,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_coupon_member FOREIGN KEY (bind_member_id) REFERENCES a_member(id) ON DELETE SET NULL
);

CREATE UNIQUE INDEX idx_coupon_code_unique_active ON a_promote_coupon(code) WHERE delete_time IS NULL;
CREATE INDEX idx_coupon_status ON a_promote_coupon(status);
CREATE INDEX idx_coupon_bind_member_id ON a_promote_coupon(bind_member_id);

COMMENT ON TABLE a_promote_coupon IS '优惠券表，定义了系统中的各类优惠券，包括其类型、折扣、有效期、使用规则等。';
COMMENT ON COLUMN a_promote_coupon.id IS '主键ID';
COMMENT ON COLUMN a_promote_coupon.code IS '优惠券码，需唯一';
COMMENT ON COLUMN a_promote_coupon.type IS '优惠券类型 (字典: coupon_type), 1-固定金额, 2-百分比折扣, 3-满减';
COMMENT ON COLUMN a_promote_coupon.status IS '状态 (字典: coupon_status), 1-可用, 2-已用, 3-过期';
COMMENT ON COLUMN a_promote_coupon.discount_value IS '折扣值 (如固定金额或百分比)';
COMMENT ON COLUMN a_promote_coupon.threshold IS '满减门槛，仅对 threshold 类型有效';
COMMENT ON COLUMN a_promote_coupon.valid_from IS '有效期开始时间';
COMMENT ON COLUMN a_promote_coupon.valid_to IS '有效期结束时间';
COMMENT ON COLUMN a_promote_coupon.applicable_model_ids IS '适用的模型ID列表 (空表示全部适用)';
COMMENT ON COLUMN a_promote_coupon.per_user_limit IS '每个用户可使用的次数';
COMMENT ON COLUMN a_promote_coupon.total_quantity IS '该优惠券的总可用次数，NULL表示不限制';
COMMENT ON COLUMN a_promote_coupon.used_quantity IS '已被使用的次数';
COMMENT ON COLUMN a_promote_coupon.bind_member_id IS '绑定的会员ID (若为空则为通用券)';
COMMENT ON COLUMN a_promote_coupon.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_promote_coupon.create_by IS '创建人';
COMMENT ON COLUMN a_promote_coupon.update_by IS '修改人';
COMMENT ON COLUMN a_promote_coupon.create_time IS '创建时间';
COMMENT ON COLUMN a_promote_coupon.update_time IS '更新时间';


-- ==================================================
-- 4. 优惠券使用日志表 (a_promote_coupon_log)
-- ==================================================
CREATE TABLE a_promote_coupon_log (
    id                  BIGSERIAL PRIMARY KEY,
    coupon_id           BIGINT NOT NULL,
    member_id             BIGINT NOT NULL,
    order_id            BIGINT NOT NULL,
    discount_applied    NUMERIC(10, 4) NOT NULL,
    used_time           TIMESTAMPTZ DEFAULT NOW(),
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_coupon_log_coupon FOREIGN KEY (coupon_id) REFERENCES a_promote_coupon(id) ON DELETE CASCADE,
    CONSTRAINT fk_coupon_log_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE,
    CONSTRAINT fk_coupon_log_order FOREIGN KEY (order_id) REFERENCES a_billing_order(id) ON DELETE CASCADE
);

CREATE INDEX idx_coupon_log_coupon_id ON a_promote_coupon_log(coupon_id);
CREATE INDEX idx_coupon_log_member_id ON a_promote_coupon_log(member_id);

COMMENT ON TABLE a_promote_coupon_log IS '记录用户使用优惠券的日志，关联了优惠券、用户和订单。';
COMMENT ON COLUMN a_promote_coupon_log.id IS '主键ID';
COMMENT ON COLUMN a_promote_coupon_log.coupon_id IS '使用的优惠券ID';
COMMENT ON COLUMN a_promote_coupon_log.member_id IS '使用的会员ID';
COMMENT ON COLUMN a_promote_coupon_log.order_id IS '关联的订单ID';
COMMENT ON COLUMN a_promote_coupon_log.discount_applied IS '本次使用实际抵扣的金额';
COMMENT ON COLUMN a_promote_coupon_log.used_time IS '使用时间';
COMMENT ON COLUMN a_promote_coupon_log.create_time IS '创建时间';
COMMENT ON COLUMN a_promote_coupon_log.update_time IS '更新时间';



-- ==================================================
-- 会员邀请记录表 (a_promote_invitation)
-- ==================================================
CREATE TABLE a_promote_invitation (
    id                  BIGSERIAL PRIMARY KEY,
    inviter_id          BIGINT NOT NULL,
    invitee_id          BIGINT NOT NULL,
    invitation_code_used VARCHAR(50) NOT NULL,
    reward_amount       NUMERIC(10, 4),
    reward_currency_id  BIGINT,
    reward_status       SMALLINT DEFAULT 1 NOT NULL,
    reward_time         TIMESTAMPTZ,
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_history_inviter FOREIGN KEY (inviter_id) REFERENCES a_member(id) ON DELETE CASCADE,
    CONSTRAINT fk_history_invitee FOREIGN KEY (invitee_id) REFERENCES a_member(id) ON DELETE CASCADE,
    CONSTRAINT fk_history_currency FOREIGN KEY (reward_currency_id) REFERENCES a_base_currency(id) ON DELETE SET NULL,
    UNIQUE (invitee_id)
);

COMMENT ON TABLE a_promote_invitation IS '会员邀请记录表，记录成功的用户邀请关系，用于分润等后续操作。';
COMMENT ON COLUMN a_promote_invitation.id IS '主键ID';
COMMENT ON COLUMN a_promote_invitation.inviter_id IS '邀请人ID (a_member.id)';
COMMENT ON COLUMN a_promote_invitation.invitee_id IS '被邀请人ID (a_member.id)，唯一';
COMMENT ON COLUMN a_promote_invitation.invitation_code_used IS '注册时使用的邀请码';
COMMENT ON COLUMN a_promote_invitation.reward_amount IS '本次邀请产生的奖励金额（分润时记录）';
COMMENT ON COLUMN a_promote_invitation.reward_currency_id IS '奖励货币ID';
COMMENT ON COLUMN a_promote_invitation.reward_status IS '奖励状态 (字典: invitation_reward_status), 1-待处理, 2-已发放, 3-已取消';
COMMENT ON COLUMN a_promote_invitation.reward_time IS '奖励计划发放时间，用于延迟发放以应对退款等情况';
COMMENT ON COLUMN a_promote_invitation.create_time IS '邀请关系确立时间（即被邀请人注册时间）';
COMMENT ON COLUMN a_promote_invitation.update_time IS '更新时间';


-- ==================================================
-- 以下为自动更新时间的触发器
-- ==================================================

CREATE TRIGGER set_promote_redemption_update_time
BEFORE UPDATE ON a_promote_redemption
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


CREATE TRIGGER set_promote_redemption_log_update_time
BEFORE UPDATE ON a_promote_redemption_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


CREATE TRIGGER set_promote_coupon_update_time
BEFORE UPDATE ON a_promote_coupon
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


CREATE TRIGGER set_promote_coupon_log_update_time
BEFORE UPDATE ON a_promote_coupon_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_promote_invitation_update_time
BEFORE UPDATE ON a_promote_invitation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();