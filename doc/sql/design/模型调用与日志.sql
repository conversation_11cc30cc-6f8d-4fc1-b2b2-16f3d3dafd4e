-- ====================================================================================
-- AIShowLab - 模型调用与日志模块数据库架构脚本 (PostgreSQL) v1.0
-- 作者: Gemini
-- 描述: 本脚本定义了模型调用请求、响应、错误和审计等相关的日志表结构。
--      遵循了项目规范，并结合了 PostgreSQL 的最佳实践。
--      v1.1 更新: 使用数据字典替代 ENUM 类型。
--      v1.2 更新: 为了性能优化，将所有基于字典的 VARCHAR 字段改为 SMALLINT。
-- ====================================================================================

-- ==================================================
-- 1. 模型调用请求日志 (a_log_model_request)
-- 注意: 此类日志表增长迅速，建议在生产环境中根据 request_time 按月或按周进行分区。
-- ==================================================
CREATE TABLE a_log_model_request (
    id              BIGSERIAL PRIMARY KEY,
    request_id      UUID NOT NULL UNIQUE,
    member_id         BIGINT NOT NULL,
    model_id        BIGINT NOT NULL,
    model_source    SMALLINT NOT NULL DEFAULT 1,
    input_summary   TEXT,
    input_params    JSONB,
    request_body    TEXT,
    status          SMALLINT NOT NULL,
    source_ip       INET,
    user_agent      TEXT,
    request_time    TIMESTAMPTZ DEFAULT NOW(),
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_request_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT fk_request_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_request_member_id ON a_log_model_request(member_id);
CREATE INDEX idx_request_model_id ON a_log_model_request(model_id);
CREATE INDEX idx_request_status ON a_log_model_request(status);
CREATE INDEX idx_request_time ON a_log_model_request(request_time);

-- 添加注释
COMMENT ON TABLE a_log_model_request IS '模型调用请求日志表，详细记录了系统接收到的每一次模型调用请求的核心信息，用于追踪、调试和分析。建议按 request_time 分区。';
COMMENT ON COLUMN a_log_model_request.id IS '主键ID';
COMMENT ON COLUMN a_log_model_request.request_id IS '请求唯一标识，用于关联 request 和 response';
COMMENT ON COLUMN a_log_model_request.member_id IS '调用会员 ID';
COMMENT ON COLUMN a_log_model_request.model_id IS '模型 ID';
COMMENT ON COLUMN a_log_model_request.model_source IS '模型来源快照 (字典: model_source), 1-第三方, 2-内部模型';
COMMENT ON COLUMN a_log_model_request.input_summary IS '脱敏后的输入摘要，用于快速预览';
COMMENT ON COLUMN a_log_model_request.input_params IS '模型调用参数（JSON 格式），如温度、top_p';
COMMENT ON COLUMN a_log_model_request.request_body IS '原始请求体（可裁剪）';
COMMENT ON COLUMN a_log_model_request.status IS '调用状态 (字典: request_status), 1-成功, 2-失败, 3-处理中';
COMMENT ON COLUMN a_log_model_request.source_ip IS '会员 IP 地址';
COMMENT ON COLUMN a_log_model_request.user_agent IS '调用设备/浏览器信息';
COMMENT ON COLUMN a_log_model_request.request_time IS '实际请求发起时间';
COMMENT ON COLUMN a_log_model_request.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_log_model_request.create_time IS '创建时间';
COMMENT ON COLUMN a_log_model_request.update_time IS '更新时间';


-- ==================================================
-- 2. 模型调用响应日志 (a_log_model_response)
-- ==================================================
CREATE TABLE a_log_model_response (
    id              BIGSERIAL PRIMARY KEY,
    request_id      UUID NOT NULL UNIQUE REFERENCES a_log_model_request(request_id) ON DELETE CASCADE,
    response_content TEXT,
    format          SMALLINT NOT NULL DEFAULT 1,
    is_stream       BOOLEAN NOT NULL DEFAULT FALSE,
    response_code   VARCHAR(50),
    duration_ms     INTEGER,
    response_time   TIMESTAMPTZ DEFAULT NOW(),
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE a_log_model_response IS '模型调用响应日志表，记录了与请求对应的模型输出结果、耗时等信息。与请求日志一起，构成了完整的调用链路记录。';
COMMENT ON COLUMN a_log_model_response.id IS '主键ID';
COMMENT ON COLUMN a_log_model_response.request_id IS '关联的请求ID';
COMMENT ON COLUMN a_log_model_response.response_content IS '原始响应内容（可裁剪或部分存储）';
COMMENT ON COLUMN a_log_model_response.format IS '响应类型 (字典: response_format), 1-纯文本, 2-JSON, 3-图片, 4-音频';
COMMENT ON COLUMN a_log_model_response.is_stream IS '是否为流式响应';
COMMENT ON COLUMN a_log_model_response.response_code IS '模型返回的状态码（如 HTTP 200）';
COMMENT ON COLUMN a_log_model_response.duration_ms IS '从请求到响应完成的耗时（毫秒）';
COMMENT ON COLUMN a_log_model_response.response_time IS '响应完成时间';
COMMENT ON COLUMN a_log_model_response.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_log_model_response.create_time IS '创建时间';
COMMENT ON COLUMN a_log_model_response.update_time IS '更新时间';


-- ==================================================
-- 3. 错误日志记录 (a_log_error)
-- ==================================================
CREATE TABLE a_log_error (
    id              BIGSERIAL PRIMARY KEY,
    source_type     SMALLINT NOT NULL,
    related_id      BIGINT,
    error_code      VARCHAR(100),
    message         TEXT NOT NULL,
    stack_trace     TEXT,
    error_time      TIMESTAMPTZ DEFAULT NOW(),
    member_id         BIGINT,
    model_id        BIGINT,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_error_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT fk_error_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE SET NULL
);

CREATE INDEX idx_error_source_type ON a_log_error(source_type);
CREATE INDEX idx_error_related_id ON a_log_error(related_id);
CREATE INDEX idx_error_time ON a_log_error(error_time);

COMMENT ON TABLE a_log_error IS '统一错误日志表，用于记录系统运行过程中发生的各类异常，包括业务异常、程序异常、第三方服务错误等。建议按 error_time 分区。';
COMMENT ON COLUMN a_log_error.id IS '主键ID';
COMMENT ON COLUMN a_log_error.source_type IS '错误来源类型 (字典: error_source), 1-系统, 2-模型, 3-网络, 4-数据库';
COMMENT ON COLUMN a_log_error.related_id IS '关联记录 ID，如 a_log_model_request 的 id';
COMMENT ON COLUMN a_log_error.error_code IS '错误码，如系统定义或模型响应码';
COMMENT ON COLUMN a_log_error.message IS '错误信息';
COMMENT ON COLUMN a_log_error.stack_trace IS '异常堆栈（程序异常时记录）';
COMMENT ON COLUMN a_log_error.error_time IS '错误发生时间';
COMMENT ON COLUMN a_log_error.member_id IS '触发错误的会员ID';
COMMENT ON COLUMN a_log_error.model_id IS '若与模型相关，则记录模型ID';
COMMENT ON COLUMN a_log_error.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_log_error.create_time IS '创建时间';
COMMENT ON COLUMN a_log_error.update_time IS '更新时间';


-- ==================================================
-- 4. 审计日志 (a_log_audit)
-- ==================================================
CREATE TABLE a_log_audit (
    id              BIGSERIAL PRIMARY KEY,
    action_type     SMALLINT NOT NULL,
    actor_member_id   BIGINT NOT NULL,
    target_member_id  BIGINT,
    description     TEXT,
    data_snapshot   JSONB,
    audit_time      TIMESTAMPTZ DEFAULT NOW(),
    ip_address      INET,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_audit_actor_member FOREIGN KEY (actor_member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT fk_audit_target_member FOREIGN KEY (target_member_id) REFERENCES a_member(id) ON DELETE SET NULL
);

CREATE INDEX idx_audit_action_type ON a_log_audit(action_type);
CREATE INDEX idx_audit_actor_member_id ON a_log_audit(actor_member_id);
CREATE INDEX idx_audit_target_member_id ON a_log_audit(target_member_id);
CREATE INDEX idx_audit_time ON a_log_audit(audit_time);

COMMENT ON TABLE a_log_audit IS '审计日志表，用于记录系统中所有关键性的会员操作和系统事件，如会员登录、权限变更、资金操作等，确保所有重要行为可追溯。建议按 audit_time 分区。';
COMMENT ON COLUMN a_log_audit.id IS '主键ID';
COMMENT ON COLUMN a_log_audit.action_type IS '操作类型 (字典: audit_action), 1-会员登录, 2-会员登出, 10-角色创建, 11-权限授予, 20-余额充值, 21-余额消耗';
COMMENT ON COLUMN a_log_audit.actor_member_id IS '执行操作的会员 ID';
COMMENT ON COLUMN a_log_audit.target_member_id IS '被操作的会员 ID（如修改他人权限）';
COMMENT ON COLUMN a_log_audit.description IS '审计描述说明';
COMMENT ON COLUMN a_log_audit.data_snapshot IS '操作前后的数据快照（JSONB 格式）';
COMMENT ON COLUMN a_log_audit.audit_time IS '操作时间';
COMMENT ON COLUMN a_log_audit.ip_address IS '操作来源 IP';
COMMENT ON COLUMN a_log_audit.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_log_audit.create_time IS '创建时间';
COMMENT ON COLUMN a_log_audit.update_time IS '更新时间';


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新时间的表绑定触发器
CREATE TRIGGER set_log_model_request_update_time
BEFORE UPDATE ON a_log_model_request
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_log_model_response_update_time
BEFORE UPDATE ON a_log_model_response
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_log_error_update_time
BEFORE UPDATE ON a_log_error
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_log_audit_update_time
BEFORE UPDATE ON a_log_audit
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- ==================================================
-- 脚本结束
-- ==================================================
