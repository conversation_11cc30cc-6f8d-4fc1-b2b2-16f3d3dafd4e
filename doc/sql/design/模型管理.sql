-- ====================================================================================
-- AIShowLab - AI 模型管理模块数据库架构脚本 (PostgreSQL) v1.2
-- 作者: Gemini
-- 描述: 本脚本定义了模型、分类、特性、模板、格式和可见性等相关的表结构。
--      v1.2 更新: 根据指令调整外键策略，增强数据删除操作的安全性。
--      v1.3 更新: 使用数据字典替代 ENUM 类型。
--      v1.4 更新: 为了性能优化，将所有基于字典的 VARCHAR 字段改为 SMALLINT。
-- ====================================================================================

-- ==================================================
-- 1. 模型分类表 (a_model_category)
-- ==================================================
CREATE TABLE a_model_category (
    id              BIGSERIAL PRIMARY KEY,
    code            VARCHAR(50) NOT NULL,
    name            VARCHAR(100) NOT NULL,
    sort_order      INTEGER DEFAULT 0 NOT NULL,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT category_code_not_empty CHECK (trim(code) <> ''),
    CONSTRAINT category_name_not_empty CHECK (trim(name) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_category_code_unique_active ON a_model_category(code) WHERE delete_time IS NULL;

COMMENT ON TABLE a_model_category IS '模型分类表，用于对AI模型进行归类，如文本生成、图像生成、音频处理等，方便前端展示和筛选。';
COMMENT ON COLUMN a_model_category.id IS '主键ID';
COMMENT ON COLUMN a_model_category.code IS '分类唯一标识，如: text, image, audio';
COMMENT ON COLUMN a_model_category.name IS '分类名称，用于前端展示';
COMMENT ON COLUMN a_model_category.sort_order IS '排序，desc';
COMMENT ON COLUMN a_model_category.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_category.create_by IS '创建人';
COMMENT ON COLUMN a_model_category.update_by IS '修改人';
COMMENT ON COLUMN a_model_category.create_time IS '创建时间';
COMMENT ON COLUMN a_model_category.update_time IS '更新时间';



-- ==================================================
-- 2. 模型供应商表 (a_model_provider)
-- ==================================================
CREATE TABLE a_model_provider (
    id              BIGSERIAL PRIMARY KEY,
    provider_name   VARCHAR(100) NOT NULL,
    provider_key    VARCHAR(50) NOT NULL UNIQUE,
    channel_type    VARCHAR(50) NOT NULL DEFAULT 1,
    sort_order      INTEGER DEFAULT 0 NOT NULL,
    base_url        TEXT NOT NULL,
    description     TEXT,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE a_model_provider IS '模型供应商表，用于存储不同AI模型供应商的基础信息。';
COMMENT ON COLUMN a_model_provider.id IS '主键ID';
COMMENT ON COLUMN a_model_provider.provider_name IS '供应商名称，用于显示 (例如: OpenAI, Google)';
COMMENT ON COLUMN a_model_provider.provider_key IS '供应商唯一标识，用于程序内部关联 (例如: openai, google)';
COMMENT ON COLUMN a_model_provider.channel_type IS '渠道类型 (字典: provider_channel_type), 如: 1-official, 2-agent';
COMMENT ON COLUMN a_model_provider.sort_order IS '排序，desc';
COMMENT ON COLUMN a_model_provider.base_url IS '供应商基础API URL';
COMMENT ON COLUMN a_model_provider.description IS '备注信息';
COMMENT ON COLUMN a_model_provider.create_by IS '创建人';
COMMENT ON COLUMN a_model_provider.update_by IS '修改人';
COMMENT ON COLUMN a_model_provider.create_time IS '创建时间';
COMMENT ON COLUMN a_model_provider.update_time IS '更新时间';


-- ==================================================
-- 3. AI 模型主表 (a_model)
-- ==================================================
CREATE TABLE a_model (
    id                  BIGSERIAL PRIMARY KEY,
    name                VARCHAR(100) NOT NULL,
    code                VARCHAR(100) NOT NULL,
    source              SMALLINT NOT NULL DEFAULT 1,
    category_id         BIGINT NOT NULL,
    provider_id         BIGINT NOT NULL,
    version             VARCHAR(50) NOT NULL,
    api_endpoint        TEXT,
    invoke_method       SMALLINT DEFAULT 1,
    supports_stream     BOOLEAN DEFAULT FALSE,
    supports_function   BOOLEAN DEFAULT FALSE,
    sort_order          INTEGER DEFAULT 0 NOT NULL,
    description         TEXT,
    is_enabled          BOOLEAN DEFAULT TRUE NOT NULL,
    delete_time         TIMESTAMPTZ,
    artifact_path       TEXT,
    artifact_type       VARCHAR(50),
    create_by           VARCHAR(64),
    update_by           VARCHAR(64),
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_model_category FOREIGN KEY (category_id) REFERENCES a_model_category(id) ON DELETE SET NULL,
    CONSTRAINT fk_model_provider FOREIGN KEY (provider_id) REFERENCES a_model_provider(id) ON DELETE SET NULL,
    CONSTRAINT model_code_not_empty CHECK (trim(code) <> ''),
    CONSTRAINT model_name_not_empty CHECK (trim(name) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_model_code_unique_active ON a_model(code) WHERE delete_time IS NULL;
-- 外键索引
CREATE INDEX idx_model_category_id ON a_model(category_id);
-- 常用查询索引
CREATE INDEX idx_model_provider_id ON a_model(provider_id);
CREATE INDEX idx_model_is_enabled ON a_model(is_enabled);


COMMENT ON TABLE a_model IS 'AI模型主表，定义了系统中所有可用AI模型的核心属性，如名称、编码、提供商、API端点等。';
COMMENT ON COLUMN a_model.id IS '主键ID';
COMMENT ON COLUMN a_model.name IS '模型名称（可用于内部展示）';
COMMENT ON COLUMN a_model.code IS '模型唯一编码（如 gpt-4-turbo）';
COMMENT ON COLUMN a_model.source IS '模型来源 (字典: model_source), 1-第三方, 2-内部模型';
COMMENT ON COLUMN a_model.category_id IS '外键，关联 a_model_category.id';
COMMENT ON COLUMN a_model.provider_id IS '外键，关联 a_model_provider.id';
COMMENT ON COLUMN a_model.version IS '模型版本，如 "2024-05"';
COMMENT ON COLUMN a_model.api_endpoint IS '接口调用地址';
COMMENT ON COLUMN a_model.invoke_method IS '调用方式 (字典: model_invoke_method), 1-HTTP, 2-WebSocket, 3-SDK';
COMMENT ON COLUMN a_model.supports_stream IS '是否支持流式响应';
COMMENT ON COLUMN a_model.supports_function IS '是否支持函数调用';
COMMENT ON COLUMN a_model.sort_order IS '排序，desc';
COMMENT ON COLUMN a_model.description IS '模型详细描述';
COMMENT ON COLUMN a_model.is_enabled IS '是否启用';
COMMENT ON COLUMN a_model.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model.artifact_path IS '模型制品路径 (如: s3://bucket/models/llama3, /data/models/qwen)';
COMMENT ON COLUMN a_model.artifact_type IS '制品类型 (如: gguf, safetensors, pytorch_native)';
COMMENT ON COLUMN a_model.create_by IS '创建人';
COMMENT ON COLUMN a_model.update_by IS '修改人';
COMMENT ON COLUMN a_model.create_time IS '创建时间';
COMMENT ON COLUMN a_model.update_time IS '更新时间';


-- ==================================================
-- 4. 模型特性表 (a_model_feature)
-- ==================================================
CREATE TABLE a_model_feature (
    id              BIGSERIAL PRIMARY KEY,
    model_id        BIGINT NOT NULL,
    key             VARCHAR(50) NOT NULL,
    value           JSONB NOT NULL,
    description     TEXT,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_feature_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE CASCADE,
    CONSTRAINT feature_key_not_empty CHECK (trim(key) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_feature_model_key_unique_active ON a_model_feature(model_id, key) WHERE delete_time IS NULL;

COMMENT ON TABLE a_model_feature IS '模型特性表，以键值对的形式存储模型的各种可配置属性或元数据，如上下文长度、多语言支持等，实现模型属性的灵活扩展。';
COMMENT ON COLUMN a_model_feature.id IS '主键ID';
COMMENT ON COLUMN a_model_feature.model_id IS '外键，关联 a_model.id';
COMMENT ON COLUMN a_model_feature.key IS '特性键，如 context_length、multilingual';
COMMENT ON COLUMN a_model_feature.value IS '特性值，使用 JSONB 以支持复杂结构，如 "8192" 或 {"resolutions": ["1024x1024"]}';
COMMENT ON COLUMN a_model_feature.description IS '特性说明';
COMMENT ON COLUMN a_model_feature.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_feature.create_by IS '创建人';
COMMENT ON COLUMN a_model_feature.update_by IS '修改人';
COMMENT ON COLUMN a_model_feature.create_time IS '创建时间';
COMMENT ON COLUMN a_model_feature.update_time IS '更新时间';


-- ==================================================
-- 5. 模型输出格式定义表 (a_model_output_format)
-- ==================================================
CREATE TABLE a_model_output_format (
    id              BIGSERIAL PRIMARY KEY,
    model_id        BIGINT NOT NULL,
    output_type     SMALLINT NOT NULL,
    supports_stream BOOLEAN DEFAULT FALSE NOT NULL,
    max_tokens      INTEGER DEFAULT 2048 NOT NULL,
    description     TEXT,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_output_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE CASCADE,
    CONSTRAINT max_tokens_positive CHECK (max_tokens > 0)
);

CREATE INDEX idx_output_format_model_id ON a_model_output_format(model_id);

COMMENT ON TABLE a_model_output_format IS '模型输出格式定义表，用于声明单个模型具体支持的输出类型（如json, text）及其相关约束，如最大token数。';
COMMENT ON COLUMN a_model_output_format.id IS '主键ID';
COMMENT ON COLUMN a_model_output_format.model_id IS '外键，关联 a_model.id';
COMMENT ON COLUMN a_model_output_format.output_type IS '支持的输出类型 (字典: model_output_type), 1-JSON, 2-文本, 3-图片, 4-音频';
COMMENT ON COLUMN a_model_output_format.supports_stream IS '该输出格式下是否支持流式返回';
COMMENT ON COLUMN a_model_output_format.max_tokens IS '该输出格式下的建议最大 token 数';
COMMENT ON COLUMN a_model_output_format.description IS '输出备注说明';
COMMENT ON COLUMN a_model_output_format.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_output_format.create_by IS '创建人';
COMMENT ON COLUMN a_model_output_format.update_by IS '修改人';
COMMENT ON COLUMN a_model_output_format.create_time IS '创建时间';
COMMENT ON COLUMN a_model_output_format.update_time IS '更新时间';


-- ==================================================
-- 6. 模型可见性控制表 (a_model_visibility)
-- ==================================================
CREATE TABLE a_model_visibility (
    id              BIGSERIAL PRIMARY KEY,
    model_id        BIGINT NOT NULL,
    visibility_type SMALLINT NOT NULL,
    reference_id    BIGINT NOT NULL,
    is_enabled      BOOLEAN DEFAULT TRUE NOT NULL,
    description     TEXT,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_visibility_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE CASCADE
);

CREATE INDEX idx_visibility_model_id ON a_model_visibility(model_id);
CREATE INDEX idx_visibility_ref ON a_model_visibility(visibility_type, reference_id);

COMMENT ON TABLE a_model_visibility IS '模型可见性控制表，通过定义规则来精确控制模型对不同维度（如角色、会员、区域）的可见性，实现细粒度的模型访问控制。';
COMMENT ON COLUMN a_model_visibility.id IS '主键ID';
COMMENT ON COLUMN a_model_visibility.model_id IS '外键，关联 a_model.id';
COMMENT ON COLUMN a_model_visibility.visibility_type IS '可见性控制的类型 (字典: model_visibility_type), 1-按角色, 2-按会员, 3-按地区';
COMMENT ON COLUMN a_model_visibility.reference_id IS '对应的类型ID（如角色ID, 用户ID等）';
COMMENT ON COLUMN a_model_visibility.is_enabled IS '该条可见性规则是否启用';
COMMENT ON COLUMN a_model_visibility.description IS '详细描述';
COMMENT ON COLUMN a_model_visibility.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_visibility.create_by IS '创建人';
COMMENT ON COLUMN a_model_visibility.update_by IS '修改人';
COMMENT ON COLUMN a_model_visibility.create_time IS '创建时间';
COMMENT ON COLUMN a_model_visibility.update_time IS '更新时间';


-- ==================================================
-- 7. 模型API密钥表 (a_model_api_key)
-- ==================================================
CREATE TABLE a_model_api_key (
    id                      BIGSERIAL PRIMARY KEY,
    provider_id             BIGINT NOT NULL,
    api_key                 TEXT NOT NULL,
    api_endpoint_override   TEXT,
    priority                SMALLINT NOT NULL DEFAULT 1,
    weight                  SMALLINT NOT NULL DEFAULT 1,
    quota                   NUMERIC(12, 4) NOT NULL DEFAULT 0,
    used_quota              NUMERIC(12, 4) NOT NULL DEFAULT 0,
	currency_id             int8 NOT NULL,
    status                  VARCHAR(50) NOT NULL DEFAULT 'inactive',
    description             TEXT,
    last_used_time          TIMESTAMPTZ,
    delete_time             TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT fk_model_api_key_provider FOREIGN KEY (provider_id) REFERENCES a_model_provider(id)
);

-- 创建索引
CREATE INDEX idx_model_api_key_provider_id_status ON a_model_api_key(provider_id, status);
CREATE INDEX idx_model_api_key_priority ON a_model_api_key(priority);

-- 添加注释
COMMENT ON TABLE a_model_api_key IS '模型API密钥表，用于统一存储、管理和监控所有第三方服务的API密钥。';
COMMENT ON COLUMN a_model_api_key.id IS '主键ID';
COMMENT ON COLUMN a_model_api_key.provider_id IS '供应商ID，关联 a_model_provider.id';
COMMENT ON COLUMN a_model_api_key.api_key IS 'API密钥的加密存储值。必须在应用层加密后存储，严禁明文。';
COMMENT ON COLUMN a_model_api_key.api_endpoint_override IS '代理接入点URL。若非空，则优先使用此URL代替供应商默认地址。';
COMMENT ON COLUMN a_model_api_key.priority IS '优先级，数字越小，优先级越高。用于主备密钥策略。字典: api_key_priority';
COMMENT ON COLUMN a_model_api_key.weight IS '权重，用于在相同优先级下进行加权轮询。字典: api_key_weight';
COMMENT ON COLUMN a_model_api_key.quota IS '总额度，单位通常为美元或人民币。';
COMMENT ON COLUMN a_model_api_key.used_quota IS '已用额度，由后台任务或调用逻辑更新。';
COMMENT ON COLUMN a_model_api_key.currency_id IS '额度的货币单位';
COMMENT ON COLUMN a_model_api_key.status IS '密钥状态 (字典: api_key_status)，如: active, inactive, exhausted';
COMMENT ON COLUMN a_model_api_key.description IS '备注，可记录密钥来源、用途等信息。';
COMMENT ON COLUMN a_model_api_key.last_used_time IS '该密钥最近一次被使用的时间。';
COMMENT ON COLUMN a_model_api_key.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_api_key.create_by IS '创建人';
COMMENT ON COLUMN a_model_api_key.update_by IS '修改人'; 
COMMENT ON COLUMN a_model_api_key.create_time IS '创建时间';
COMMENT ON COLUMN a_model_api_key.update_time IS '更新时间';


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新时间的表绑定触发器
CREATE TRIGGER set_model_category_update_time
BEFORE UPDATE ON a_model_category
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_model_update_time
BEFORE UPDATE ON a_model
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_model_feature_update_time
BEFORE UPDATE ON a_model_feature
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_model_output_format_update_time
BEFORE UPDATE ON a_model_output_format
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_model_visibility_update_time
BEFORE UPDATE ON a_model_visibility
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- 为 a_model_api_key 表绑定触发器
CREATE TRIGGER set_model_api_key_update_time
BEFORE UPDATE ON a_model_api_key
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- 为 a_model_provider 表绑定触发器
CREATE TRIGGER set_model_provider_update_time
BEFORE UPDATE ON a_model_provider
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- ==================================================
-- 8. 模型调用统计日志表 (a_model_call_log)
-- 注意: 此表专门用于统计分析，整合了请求和响应的关键信息
-- 与 a_log_model_request/a_log_model_response 表互补，后者用于详细日志记录
-- ==================================================
CREATE TABLE a_model_call_log (
    id              BIGSERIAL PRIMARY KEY,
    request_id      UUID NOT NULL,
    member_id       BIGINT NOT NULL,
    model_id        BIGINT NOT NULL,
    status          INTEGER NOT NULL DEFAULT 3 COMMENT '调用状态 (字典: model_call_status)',
    input_tokens    INTEGER DEFAULT 0,
    output_tokens   INTEGER DEFAULT 0,
    total_tokens    INTEGER DEFAULT 0,
    response_time   INTEGER,
    error_code      VARCHAR(50),
    error_message   TEXT,
    source_ip       INET,
    user_agent      TEXT,
    request_time    TIMESTAMPTZ DEFAULT NOW(),
    response_time_ts TIMESTAMPTZ,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_call_log_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT fk_call_log_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE SET NULL,
    CONSTRAINT response_time_positive CHECK (response_time IS NULL OR response_time >= 0),
    CONSTRAINT tokens_non_negative CHECK (
        input_tokens >= 0 AND
        output_tokens >= 0 AND
        total_tokens >= 0
    )
);

-- 创建索引（针对统计查询优化）
CREATE INDEX idx_call_log_member_id ON a_model_call_log(member_id);
CREATE INDEX idx_call_log_model_id ON a_model_call_log(model_id);
CREATE INDEX idx_call_log_status ON a_model_call_log(status);
CREATE INDEX idx_call_log_request_time ON a_model_call_log(request_time);
CREATE INDEX idx_call_log_member_model ON a_model_call_log(member_id, model_id);
CREATE INDEX idx_call_log_model_status_time ON a_model_call_log(model_id, status, request_time);

-- 为性能统计优化的复合索引
CREATE INDEX idx_call_log_stats_recent ON a_model_call_log(model_id, request_time)
WHERE request_time >= CURRENT_DATE - INTERVAL '30 days';

-- 添加注释
COMMENT ON TABLE a_model_call_log IS '模型调用统计日志表，专门用于统计分析和性能监控。整合了请求和响应的关键信息，支持高效的统计查询。建议按 request_time 分区。';
COMMENT ON COLUMN a_model_call_log.id IS '主键ID';
COMMENT ON COLUMN a_model_call_log.request_id IS '请求唯一标识，可关联详细日志表';
COMMENT ON COLUMN a_model_call_log.member_id IS '调用会员ID';
COMMENT ON COLUMN a_model_call_log.model_id IS '模型ID';
COMMENT ON COLUMN a_model_call_log.status IS '调用状态 (字典: model_call_status): 1-成功, 2-失败, 3-处理中, 4-超时';
COMMENT ON COLUMN a_model_call_log.input_tokens IS '输入Token数量';
COMMENT ON COLUMN a_model_call_log.output_tokens IS '输出Token数量';
COMMENT ON COLUMN a_model_call_log.total_tokens IS '总Token数量（input + output）';
COMMENT ON COLUMN a_model_call_log.response_time IS '响应时间（毫秒）';
COMMENT ON COLUMN a_model_call_log.error_code IS '错误码（失败时记录）';
COMMENT ON COLUMN a_model_call_log.error_message IS '错误信息（失败时记录）';
COMMENT ON COLUMN a_model_call_log.source_ip IS '调用来源IP';
COMMENT ON COLUMN a_model_call_log.user_agent IS '用户代理信息';
COMMENT ON COLUMN a_model_call_log.request_time IS '请求发起时间';
COMMENT ON COLUMN a_model_call_log.response_time_ts IS '响应完成时间';
COMMENT ON COLUMN a_model_call_log.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_model_call_log.create_time IS '创建时间';
COMMENT ON COLUMN a_model_call_log.update_time IS '更新时间';

-- 为 a_model_call_log 表绑定触发器
CREATE TRIGGER set_model_call_log_update_time
BEFORE UPDATE ON a_model_call_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();



-- 会员模型收藏表
-- 用于存储会员收藏的AI模型信息

CREATE TABLE a_member_favorite_model (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL COMMENT '会员ID',
    model_id BIGINT NOT NULL COMMENT '模型ID',
    delete_time TIMESTAMPTZ NULL COMMENT '删除时间（软删除）',
    create_time TIMESTAMPTZ DEFAULT now() COMMENT '创建时间',
    update_time TIMESTAMPTZ DEFAULT now() COMMENT '更新时间'
);

-- 创建索引
CREATE INDEX idx_member_favorite_model_member_id ON a_member_favorite_model(member_id);
CREATE INDEX idx_member_favorite_model_model_id ON a_member_favorite_model(model_id);
CREATE INDEX idx_member_favorite_model_create_time ON a_member_favorite_model(create_time);
CREATE INDEX idx_member_favorite_model_delete_time ON a_member_favorite_model(delete_time);

-- 创建唯一约束（会员+模型的组合唯一）
CREATE UNIQUE INDEX uk_member_favorite_model ON a_member_favorite_model(member_id, model_id) WHERE delete_time IS NULL;

-- 添加外键约束
ALTER TABLE a_member_favorite_model
ADD CONSTRAINT fk_member_favorite_model_member_id
FOREIGN KEY (member_id) REFERENCES a_member(id);

ALTER TABLE a_member_favorite_model
ADD CONSTRAINT fk_member_favorite_model_model_id
FOREIGN KEY (model_id) REFERENCES a_model(id);

CREATE TRIGGER set_member_favorite_model_update_time
BEFORE UPDATE ON a_member_favorite_model
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- 添加表注释
COMMENT ON TABLE a_member_favorite_model IS '会员模型收藏表';
COMMENT ON COLUMN a_member_favorite_model.id IS '主键ID';
COMMENT ON COLUMN a_member_favorite_model.member_id IS '会员ID';
COMMENT ON COLUMN a_member_favorite_model.model_id IS '模型ID';
COMMENT ON COLUMN a_member_favorite_model.create_time IS '创建时间';
COMMENT ON COLUMN a_member_favorite_model.update_time IS '更新时间';
COMMENT ON COLUMN a_member_favorite_model.delete_time IS '删除时间（软删除）';

-- ==================================================
-- 脚本结束
-- ==================================================
