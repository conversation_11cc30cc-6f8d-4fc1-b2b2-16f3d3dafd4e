-- ====================================================================================
-- AIShowLab - 计费与消耗模块数据库架构脚本 (PostgreSQL) v1.1
-- 作者: Gemini
-- 描述: 本脚本定义了计费方案、价格、使用记录、余额、流水等相关的表结构。
-- ====================================================================================

-- ==================================================
-- 计费方案表 (a_billing_plan)
-- ==================================================
CREATE TABLE a_billing_plan (
    id              BIGSERIAL PRIMARY KEY,
    name            VARCHAR(100) NOT NULL,
    unit            SMALLINT NOT NULL,
    sort_order      INTEGER NOT NULL DEFAULT 0,
    description     TEXT,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT plan_name_not_empty CHECK (trim(name) <> '')
);

COMMENT ON TABLE a_billing_plan IS '计费方案表，用于定义不同的计费模型，例如“按Token计费”、“按次计费”等。它是价格策略的基础。';
COMMENT ON COLUMN a_billing_plan.id IS '主键ID';
COMMENT ON COLUMN a_billing_plan.name IS '计费方案名称（如: 按 token 计费）';
COMMENT ON COLUMN a_billing_plan.unit IS '计费单位 (字典: billing_unit), 1-Token, 2-次, 3-张(图), 4-秒(音频)';
COMMENT ON COLUMN a_func_role.sort_order IS '排序，desc';
COMMENT ON COLUMN a_billing_plan.description IS '方案详细描述';
COMMENT ON COLUMN a_billing_plan.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_plan.create_by IS '创建人';
COMMENT ON COLUMN a_billing_plan.update_by IS '修改人';
COMMENT ON COLUMN a_billing_plan.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_plan.update_time IS '更新时间';



-- ==================================================
-- 计费套餐表 (a_billing_package)
-- ==================================================
CREATE TABLE a_billing_package (
    id                  BIGSERIAL PRIMARY KEY,
    owner_member_id       BIGINT,
    code                VARCHAR(50) NOT NULL,
    name                VARCHAR(100) NOT NULL,
    description         TEXT,
    type                SMALLINT NOT NULL DEFAULT 1,
    price               NUMERIC(10, 2) NOT NULL,
    currency_id         BIGINT NOT NULL,
    credits_granted     NUMERIC(12, 4) NOT NULL,
    validity_days       INTEGER,
    renewal_interval    INTERVAL,
    renewal_interval_unit SMALLINT NOT NULL DEFAULT 1,
    member_level_grant    SMALLINT NOT NULL,
    status              SMALLINT NOT NULL DEFAULT 2,
    sort_order          INTEGER NOT NULL DEFAULT 0,
    delete_time         TIMESTAMPTZ,
    create_by           VARCHAR(64),
    update_by           VARCHAR(64),
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_package_owner FOREIGN KEY (owner_member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT fk_package_currency FOREIGN KEY (currency_id) REFERENCES a_base_currency(id) ON DELETE SET NULL,
    CONSTRAINT package_code_not_empty CHECK (trim(code) <> ''),
    CONSTRAINT package_price_non_negative CHECK (price >= 0)
);

CREATE UNIQUE INDEX idx_package_code_unique ON a_billing_package(code) WHERE delete_time IS NULL;
CREATE INDEX idx_package_status ON a_billing_package(status);

COMMENT ON TABLE a_billing_package IS '计费套餐表，定义系统中所有可供用户购买的商品，如“月度会员”、“一次性资源包”等。';
COMMENT ON COLUMN a_billing_package.id IS '主键ID';
COMMENT ON COLUMN a_billing_package.owner_member_id IS '套餐所有者ID，NULL表示为系统预设套餐，非NULL表示为会员创建的助手定价套餐';
COMMENT ON COLUMN a_billing_package.code IS '套餐唯一标识，程序中使用';
COMMENT ON COLUMN a_billing_package.name IS '套餐名称';
COMMENT ON COLUMN a_billing_package.description IS '套餐详细描述';
COMMENT ON COLUMN a_billing_package.type IS '套餐类型 (字典: package_type), 1-一次性, 2-订阅';
COMMENT ON COLUMN a_billing_package.price IS '套餐标价';
COMMENT ON COLUMN a_billing_package.currency_id IS '外键，关联 a_base_currency.id';
COMMENT ON COLUMN a_billing_package.credits_granted IS '购买后授予用户的点数/积分/Token数量';
COMMENT ON COLUMN a_billing_package.validity_days IS '授予的点数/积分的有效期（天），NULL表示永久';
COMMENT ON COLUMN a_billing_package.renewal_interval IS '(针对订阅类型) 续订周期，如 "1 month"';
COMMENT ON COLUMN a_billing_package.renewal_interval_unit IS '(针对订阅类型) 续订周期单位（字典 renewal_interval_unit：1-周，2-月，3-半年，4-年，5-2年）';
COMMENT ON COLUMN a_billing_package.member_level_grant IS '购买此套餐后授予的会员等级 (关联 a_member.member_level)';
COMMENT ON COLUMN a_billing_package.status IS '套餐状态 (字典: package_status), 1-草稿, 2-上架, 3-下架';
COMMENT ON COLUMN a_billing_package.sort_order IS '套餐在前端的展示排序';
COMMENT ON COLUMN a_billing_package.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_package.create_by IS '创建人';
COMMENT ON COLUMN a_billing_package.update_by IS '修改人';
COMMENT ON COLUMN a_billing_package.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_package.update_time IS '更新时间';


-- ==================================================
-- 模型价格表 (a_billing_price)
-- ==================================================
CREATE TABLE a_billing_price (
    id              BIGSERIAL PRIMARY KEY,
    model_id        BIGINT NOT NULL,
    plan_id         BIGINT NOT NULL,
    member_level    SMALLINT NOT NULL DEFAULT 1,
    price           NUMERIC(10, 4) NOT NULL,
    currency_id     BIGINT NOT NULL,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_price_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE CASCADE,
    CONSTRAINT fk_price_plan FOREIGN KEY (plan_id) REFERENCES a_billing_plan(id) ON DELETE CASCADE,
    CONSTRAINT fk_price_currency FOREIGN KEY (currency_id) REFERENCES a_base_currency(id) ON DELETE SET NULL,
    CONSTRAINT price_non_negative CHECK (price >= 0)
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_price_model_plan_level_unique ON a_billing_price(model_id, plan_id, member_level) WHERE delete_time IS NULL;
CREATE INDEX idx_price_model_id ON a_billing_price(model_id);
CREATE INDEX idx_price_plan_id ON a_billing_price(plan_id);

COMMENT ON TABLE a_billing_price IS '模型价格表，用于定义具体模型在不同计费方案和会员等级下的单价，是计费系统的核心规则表。';
COMMENT ON COLUMN a_billing_price.id IS '主键ID';
COMMENT ON COLUMN a_billing_price.model_id IS '外键，关联 a_model.id';
COMMENT ON COLUMN a_billing_price.plan_id IS '外键，关联 a_billing_plan.id';
COMMENT ON COLUMN a_billing_price.member_level IS '会员等级 (字典: member_level), 1-免费版, 2-VIP版, 3-企业版';
COMMENT ON COLUMN a_billing_price.price IS '单价，如 0.001/token';
COMMENT ON COLUMN a_billing_price.currency_id IS '外键，关联 a_base_currency.id';
COMMENT ON COLUMN a_billing_price.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_price.create_by IS '创建人';
COMMENT ON COLUMN a_billing_price.update_by IS '修改人';
COMMENT ON COLUMN a_billing_price.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_price.update_time IS '更新时间';


-- ==================================================
-- 会员使用记录表 (a_billing_usage)
-- ==================================================
CREATE TABLE a_billing_usage (
    id              BIGSERIAL PRIMARY KEY,
    member_id         BIGINT NOT NULL,
    model_id        BIGINT NOT NULL,
    plan_id         BIGINT NOT NULL,
    unit            SMALLINT NOT NULL,
    amount          NUMERIC(12, 4) NOT NULL,
    duration_ms     INTEGER,
    result_size     INTEGER,
    used_time       TIMESTAMPTZ DEFAULT NOW(),
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_usage_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT amount_non_negative CHECK (amount >= 0)
);

CREATE INDEX idx_usage_member_id ON a_billing_usage(member_id);
CREATE INDEX idx_usage_model_id ON a_billing_usage(model_id);
CREATE INDEX idx_usage_used_time ON a_billing_usage(used_time);

COMMENT ON TABLE a_billing_usage IS '会员使用模型记录表，详细记录了每一次模型调用的消耗情况，是生成账单和流水的基础数据。';
COMMENT ON COLUMN a_billing_usage.id IS '主键ID';
COMMENT ON COLUMN a_billing_usage.member_id IS '会员ID，关联 a_member.id';
COMMENT ON COLUMN a_billing_usage.model_id IS '模型ID，关联 a_model.id';
COMMENT ON COLUMN a_billing_usage.plan_id IS '计费方案ID，关联 a_billing_plan.id';
COMMENT ON COLUMN a_billing_usage.unit IS '计费单位 (字典: billing_unit), 1-Token, 2-次, 3-张(图), 4-秒(音频)';
COMMENT ON COLUMN a_billing_usage.amount IS '本次使用量';
COMMENT ON COLUMN a_billing_usage.duration_ms IS '请求耗时（毫秒）';
COMMENT ON COLUMN a_billing_usage.result_size IS '返回结果大小（字节）';
COMMENT ON COLUMN a_billing_usage.used_time IS '使用时间';
COMMENT ON COLUMN a_billing_usage.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_usage.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_usage.update_time IS '更新时间';


-- ==================================================
-- 会员余额表 (a_billing_balance)
-- ==================================================
CREATE TABLE a_billing_balance (
    id              BIGSERIAL PRIMARY KEY,
    member_id       BIGINT NOT NULL UNIQUE,
    balance         NUMERIC(12, 4) NOT NULL DEFAULT 0,
    frozen_amount   NUMERIC(12, 4) NOT NULL DEFAULT 0,
    currency_id     BIGINT NOT NULL,
    low_threshold   NUMERIC(12, 4) NOT NULL DEFAULT 0,
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_balance_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE,
    CONSTRAINT fk_balance_currency FOREIGN KEY (currency_id) REFERENCES a_base_currency(id) ON DELETE SET NULL,
    CONSTRAINT balance_non_negative CHECK (balance >= 0),
    CONSTRAINT frozen_amount_non_negative CHECK (frozen_amount >= 0)
);

COMMENT ON TABLE a_billing_balance IS '会员余额表，用于存储每个会员的账户余额、冻结金额等财务信息，与会员为一对一关系。';
COMMENT ON COLUMN a_billing_balance.id IS '主键ID';
COMMENT ON COLUMN a_billing_balance.member_id IS '会员ID，唯一';
COMMENT ON COLUMN a_billing_balance.balance IS '可用余额';
COMMENT ON COLUMN a_billing_balance.frozen_amount IS '冻结额度（如用于待支付订单）';
COMMENT ON COLUMN a_billing_balance.currency_id IS '外键，关联 a_base_currency.id';
COMMENT ON COLUMN a_billing_balance.low_threshold IS '余额预警阈值';
COMMENT ON COLUMN a_billing_balance.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_balance.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_balance.update_time IS '更新时间';


-- ==================================================
-- 消费/充值流水表 (a_billing_transaction)
-- ==================================================
CREATE TABLE a_billing_transaction (
    id              BIGSERIAL PRIMARY KEY,
    member_id       BIGINT NOT NULL,
    type            SMALLINT NOT NULL,
    amount          NUMERIC(12, 4) NOT NULL,
    currency_id     BIGINT NOT NULL,
    reference_id    BIGINT NOT NULL,
    description     TEXT,
    transaction_time TIMESTAMPTZ DEFAULT NOW(),
    delete_time     TIMESTAMPTZ,
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_transaction_member FOREIGN KEY (member_id) REFERENCES a_member(id),
    CONSTRAINT fk_transaction_currency FOREIGN KEY (currency_id) REFERENCES a_base_currency(id) ON DELETE SET NULL
);

CREATE INDEX idx_transaction_member_id ON a_billing_transaction(member_id);
CREATE INDEX idx_transaction_type ON a_billing_transaction(type);
CREATE INDEX idx_transaction_reference_id ON a_billing_transaction(reference_id);

COMMENT ON TABLE a_billing_transaction IS '会员消费/充值流水表，记录了每一次引起余额变动的事件，如消费、充值、退款等，用于对账和历史查询。';
COMMENT ON COLUMN a_billing_transaction.id IS '主键ID';
COMMENT ON COLUMN a_billing_transaction.member_id IS '会员ID';
COMMENT ON COLUMN a_billing_transaction.type IS '交易类型 (字典: transaction_type), 1-消费, 2-充值, 3-退款, 4-赠送, 5-分润';
COMMENT ON COLUMN a_billing_transaction.amount IS '交易金额';
COMMENT ON COLUMN a_billing_transaction.currency_id IS '外键，关联 a_base_currency.id';
COMMENT ON COLUMN a_billing_transaction.reference_id IS '关联ID (如订单ID, 使用记录ID)';
COMMENT ON COLUMN a_billing_transaction.description IS '交易描述';
COMMENT ON COLUMN a_billing_transaction.transaction_time IS '交易发生时间';
COMMENT ON COLUMN a_billing_transaction.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_transaction.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_transaction.update_time IS '更新时间';


-- ==================================================
-- 订单表 (a_billing_order)
-- ==================================================
CREATE TABLE a_billing_order (
    id                      BIGSERIAL PRIMARY KEY,
    order_no                VARCHAR(64) NOT NULL,
    member_id               BIGINT NOT NULL,
    package_id              BIGINT NOT NULL,
    package_info_snapshot   JSONB NOT NULL,
    amount                  NUMERIC(10, 2) NOT NULL,
    currency_id             BIGINT NOT NULL,
    status                  SMALLINT NOT NULL DEFAULT 1,
    payment_gateway_id      BIGINT NOT NULL,
    gateway_transaction_id  VARCHAR(128),
    paid_time               TIMESTAMPTZ,
    delete_time             TIMESTAMPTZ,
    create_time             TIMESTAMPTZ DEFAULT NOW(),
    update_time             TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_order_member FOREIGN KEY (member_id) REFERENCES a_member(id),
    CONSTRAINT fk_order_package FOREIGN KEY (package_id) REFERENCES a_billing_package(id) ON DELETE SET NULL,
    CONSTRAINT fk_order_currency FOREIGN KEY (currency_id) REFERENCES a_base_currency(id) ON DELETE SET NULL,
    CONSTRAINT fk_order_payment_gateway FOREIGN KEY (payment_gateway_id) REFERENCES a_payment_gateway(id) ON DELETE RESTRICT,
    CONSTRAINT order_amount_non_negative CHECK (amount >= 0)
);

CREATE UNIQUE INDEX idx_order_no_unique ON a_billing_order(order_no) WHERE delete_time IS NULL;
CREATE INDEX idx_order_member_id ON a_billing_order(member_id);
CREATE INDEX idx_order_status ON a_billing_order(status);

COMMENT ON TABLE a_billing_order IS '购买套餐的订单表，记录了用户发起的每一次套餐购买请求及其状态。';
COMMENT ON COLUMN a_billing_order.id IS '主键ID';
COMMENT ON COLUMN a_billing_order.order_no IS '订单号，全局唯一';
COMMENT ON COLUMN a_billing_order.member_id IS '购买的会员ID';
COMMENT ON COLUMN a_billing_order.package_id IS '购买的套餐ID';
COMMENT ON COLUMN a_billing_order.package_info_snapshot IS '套餐信息快照 (JSONB)，记录下单时的套餐名称、价格等，用于对账。';
COMMENT ON COLUMN a_billing_order.amount IS '订单的最终支付金额';
COMMENT ON COLUMN a_billing_order.currency_id IS '外键，关联 a_base_currency.id';
COMMENT ON COLUMN a_billing_order.status IS '订单状态 (字典: order_status), 1-待支付, 2-已完成, 3-已取消, 4-支付失败';
COMMENT ON COLUMN a_billing_order.payment_gateway_id IS '支付网关ID, 关联 a_payment_gateway.id';
COMMENT ON COLUMN a_billing_order.gateway_transaction_id IS '支付网关返回的唯一交易流水号';
COMMENT ON COLUMN a_billing_order.paid_time IS '支付完成时间';
COMMENT ON COLUMN a_billing_order.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_billing_order.create_time IS '创建时间';
COMMENT ON COLUMN a_billing_order.update_time IS '更新时间';



-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新时间的表绑定触发器
CREATE TRIGGER set_billing_plan_update_time
BEFORE UPDATE ON a_billing_plan
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_billing_price_update_time
BEFORE UPDATE ON a_billing_price
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_billing_usage_update_time
BEFORE UPDATE ON a_billing_usage
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_billing_balance_update_time
BEFORE UPDATE ON a_billing_balance
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_billing_transaction_update_time
BEFORE UPDATE ON a_billing_transaction
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_billing_package_update_time
BEFORE UPDATE ON a_billing_package
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_billing_order_update_time
BEFORE UPDATE ON a_billing_order
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- ==================================================
-- 脚本结束
-- ==================================================
