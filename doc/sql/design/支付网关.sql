-- ==================================================
-- 7. 支付网关表 (a_payment_gateway)
-- ==================================================
CREATE TABLE a_payment_gateway (
    id              BIGSERIAL PRIMARY KEY,
    name            VARCHAR(100) NOT NULL,
    code            VARCHAR(50) NOT NULL,
    logo_url        VARCHAR(255),
    config_params   JSONB,
    status          SMALLINT DEFAULT 1 NOT NULL,
    sort_order      INTEGER DEFAULT 0 NOT NULL,
    description     TEXT,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_payment_gateway_code_unique ON a_payment_gateway(code) WHERE delete_time IS NULL;

COMMENT ON TABLE a_payment_gateway IS '支付网关配置表，用于定义系统中所有可用的支付方式，如微信支付、支付宝、PayPal等。';
COMMENT ON COLUMN a_payment_gateway.id IS '主键ID';
COMMENT ON COLUMN a_payment_gateway.name IS '支付网关名称（如：微信支付）';
COMMENT ON COLUMN a_payment_gateway.code IS '唯一编码（如：wechat_pay, alipay, paypal）';
COMMENT ON COLUMN a_payment_gateway.logo_url IS 'Logo图标地址';
COMMENT ON COLUMN a_payment_gateway.config_params IS '网关配置参数 (JSONB)，如商户ID, AppID等，敏感信息需加密处理';
COMMENT ON COLUMN a_payment_gateway.status IS '状态 (字典: payment_gateway_status), 1-启用, 2-禁用';
COMMENT ON COLUMN a_payment_gateway.sort_order IS '排序值';
COMMENT ON COLUMN a_payment_gateway.description IS '描述信息';
COMMENT ON COLUMN a_payment_gateway.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_payment_gateway.create_by IS '创建人';
COMMENT ON COLUMN a_payment_gateway.update_by IS '修改人';
COMMENT ON COLUMN a_payment_gateway.create_time IS '创建时间';
COMMENT ON COLUMN a_payment_gateway.update_time IS '更新时间';


-- ==================================================
-- 8. 支付网关-国家/地区映射表 (a_payment_gateway_country)
-- ==================================================
CREATE TABLE a_payment_gateway_country (
    id              BIGSERIAL PRIMARY KEY,
    gateway_id      BIGINT NOT NULL,
    country_id      BIGINT NOT NULL,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_gateway_country_gateway FOREIGN KEY (gateway_id) REFERENCES a_payment_gateway(id) ON DELETE CASCADE,
    CONSTRAINT fk_gateway_country_country FOREIGN KEY (country_id) REFERENCES a_base_country(id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX idx_gateway_country_unique ON a_payment_gateway_country(gateway_id, country_id) WHERE delete_time IS NULL;

COMMENT ON TABLE a_payment_gateway_country IS '支付网关与国家/地区的映射表，用于控制支付方式在不同地区的可用性。';
COMMENT ON COLUMN a_payment_gateway_country.id IS '主键ID';
COMMENT ON COLUMN a_payment_gateway_country.gateway_id IS '支付网关ID, 关联 a_payment_gateway.id';
COMMENT ON COLUMN a_payment_gateway_country.country_id IS '国家/地区ID, 关联 a_base_country.id';
COMMENT ON COLUMN a_payment_gateway_country.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_payment_gateway_country.create_by IS '创建人';
COMMENT ON COLUMN a_payment_gateway_country.update_by IS '修改人';
COMMENT ON COLUMN a_payment_gateway_country.create_time IS '创建时间';
COMMENT ON COLUMN a_payment_gateway_country.update_time IS '更新时间';




CREATE TRIGGER set_payment_gateway_update_time
BEFORE UPDATE ON a_payment_gateway
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_payment_gateway_country_update_time
BEFORE UPDATE ON a_payment_gateway_country
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();