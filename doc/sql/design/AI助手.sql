-- ====================================================================================
-- AIShowLab - AI 助手模块数据库架构脚本 (PostgreSQL) v1.1
-- 作者: Gemini
-- 描述: 本脚本定义了AI助手体系的核心表结构，包括助手的分类、定义、参数和用户实例。
--      该设计旨在支持聊天、识图、文生视频、唤醒词等多种交互模式。
--      v1.1 更新: 
--          - 为了性能优化，将所有基于字典的 VARCHAR 字段改为 SMALLINT。
--          - 移除 a_assistant.billing_package_id
-- ====================================================================================


-- ==================================================
-- 1. AI 助手分类表 (a_assistant_category)
-- ==================================================
CREATE TABLE a_assistant_category (
    id              BIGSERIAL PRIMARY KEY,
    pid             int8 NULL,
    type            SMALLINT NOT NULL,
    name            VARCHAR(100) NOT NULL,
    description     TEXT,
    sort_order      INTEGER DEFAULT 0,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT category_name_not_empty CHECK (trim(name) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_assistant_category_type_unique ON a_assistant_category(type) WHERE delete_time IS NULL;

COMMENT ON TABLE a_assistant_category IS 'AI助手分类表，用于在界面上对不同类型的助手进行归类，如“生活娱乐”、“办公效率”等。';
COMMENT ON COLUMN a_assistant_category.id IS '主键ID';
COMMENT ON COLUMN a_assistant_category.pid IS '父分类';
COMMENT ON COLUMN a_assistant_category.type IS '分类唯一标识 (字典: assistant_category_type), 1-生活娱乐, 2-办公效率, 3-创意趣味, 99-其他';
COMMENT ON COLUMN a_assistant_category.name IS '分类名称，用于前端展示';
COMMENT ON COLUMN a_assistant_category.description IS '分类的详细描述';
COMMENT ON COLUMN a_assistant_category.sort_order IS '前端展示排序值';
COMMENT ON COLUMN a_assistant_category.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_assistant_category.create_by IS '创建人';
COMMENT ON COLUMN a_assistant_category.update_by IS '修改人';
COMMENT ON COLUMN a_assistant_category.create_time IS '创建时间';
COMMENT ON COLUMN a_assistant_category.update_time IS '更新时间';


-- ==================================================
-- 2. AI 助手定义表 (a_assistant)
-- ==================================================
CREATE TABLE a_assistant (
    id                  BIGSERIAL PRIMARY KEY,
    category_id         BIGINT NOT NULL,
    owner_member_id       BIGINT,
    revenue_share_rate  NUMERIC(5, 4) DEFAULT 0,
    code                VARCHAR(100) NOT NULL,
    name                VARCHAR(100) NOT NULL,
    billing_package_ids VARCHAR(100) NOT NULL,
    knowledge_ids       VARCHAR(100) NOT NULL,
    description         TEXT,
    icon_url            VARCHAR(255),
    prompt_template     TEXT,
    interaction_mode    SMALLINT NOT NULL,
    model_suggestions   JSONB,
    version             VARCHAR(50),
    status              SMALLINT DEFAULT 1,
    usage_count         BIGINT DEFAULT 0,
    is_public           BOOLEAN DEFAULT FALSE,
    is_preset           BOOLEAN DEFAULT TRUE,
    delete_time         TIMESTAMPTZ,
    create_by           VARCHAR(64),
    update_by           VARCHAR(64),
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_assistant_category FOREIGN KEY (category_id) REFERENCES a_assistant_category(id) ON DELETE SET NULL,
    CONSTRAINT fk_assistant_owner_member FOREIGN KEY (owner_member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT revenue_share_rate_check CHECK (revenue_share_rate >= 0 AND revenue_share_rate <= 1),
    CONSTRAINT assistant_code_not_empty CHECK (trim(code) <> ''),
    CONSTRAINT assistant_name_not_empty CHECK (trim(name) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_assistant_code_unique ON a_assistant(code) WHERE delete_time IS NULL;
-- 外键索引
CREATE INDEX idx_assistant_category_id ON a_assistant(category_id);
-- 常用查询索引
CREATE INDEX idx_assistant_is_enabled ON a_assistant(is_enabled);
CREATE INDEX idx_assistant_interaction_mode ON a_assistant(interaction_mode);

COMMENT ON TABLE a_assistant IS 'AI助手定义表，作为所有预设助手的“模板库”，定义了助手的核心能力和属性。';
COMMENT ON COLUMN a_assistant.id IS '主键ID';
COMMENT ON COLUMN a_assistant.category_id IS '外键，关联 a_assistant_category.id';
COMMENT ON COLUMN a_assistant.owner_member_id IS '所有者会员ID，对于定制助手，指明其归属';
COMMENT ON COLUMN a_assistant.revenue_share_rate IS '所有者的收入分成比例 (0.00 to 1.00)';
COMMENT ON COLUMN a_assistant.code IS '助手唯一编码 (如: image-recognizer, doc-writer)';
COMMENT ON COLUMN a_assistant.name IS '助手名称（如“拍照识物”）';
COMMENT ON COLUMN a_assistant.billing_package_ids IS '付费套餐IDs(多个ID之间以逗号分隔)';
COMMENT ON COLUMN a_assistant.knowledge_ids IS '知识库IDs（多个ID之间以逗号分隔）';
COMMENT ON COLUMN a_assistant.description IS '助手详细描述';
COMMENT ON COLUMN a_assistant.icon_url IS '助手的图标URL';
COMMENT ON COLUMN a_assistant.prompt_template IS '核心的系统级提示词，可包含 {{占位符}}';
COMMENT ON COLUMN a_assistant.interaction_mode IS '交互模式 (字典: assistant_interaction_mode), 1-标准对话, 2-图像分析, 3-文生视频, 4-持续监听';
COMMENT ON COLUMN a_assistant.model_suggestions IS '推荐的模型列表及优先级 (JSONB)，例如 [{"model_id": 1, "priority": 1}]';
COMMENT ON COLUMN a_assistant.version IS '助手版本号，如 "1.0.0"';
COMMENT ON COLUMN a_assistant.status IS '助手状态 (字典: assistant_status), 1-草稿, 2-待审核, 3-已发布, 4-已归档';
COMMENT ON COLUMN a_assistant.usage_count IS '使用/安装次数，用于统计热度';
COMMENT ON COLUMN a_assistant.is_public IS '是否在市场上对所有会员公开（所有者可控）';
COMMENT ON COLUMN a_assistant.is_preset IS '是否为系统预设助手，预设助手通常不允许删除';
COMMENT ON COLUMN a_assistant.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_assistant.create_by IS '创建人';
COMMENT ON COLUMN a_assistant.update_by IS '修改人';
COMMENT ON COLUMN a_assistant.create_time IS '创建时间';
COMMENT ON COLUMN a_assistant.update_time IS '更新时间';


-- ==================================================
-- 3. 助手参数定义表 (a_assistant_param)
-- ==================================================
CREATE TABLE a_assistant_param (
    id              BIGSERIAL PRIMARY KEY,
    assistant_id    BIGINT NOT NULL,
    key             VARCHAR(50) NOT NULL,
    label           VARCHAR(100) NOT NULL,
    param_type      SMALLINT NOT NULL,
    default_value   TEXT,
    options         JSONB,
    description     TEXT,
    sort_order      INTEGER DEFAULT 0,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_param_def_assistant FOREIGN KEY (assistant_id) REFERENCES a_assistant(id) ON DELETE CASCADE,
    CONSTRAINT param_def_key_not_empty CHECK (trim(key) <> '')
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_param_def_assistant_key_unique ON a_assistant_param(assistant_id, key) WHERE delete_time IS NULL;

COMMENT ON TABLE a_assistant_param IS '助手参数定义表，用于为每个助手模板定义其可配置的参数，如API密钥、唤醒词、视频风格等。';
COMMENT ON COLUMN a_assistant_param.id IS '主键ID';
COMMENT ON COLUMN a_assistant_param.assistant_id IS '外键，关联 a_assistant.id';
COMMENT ON COLUMN a_assistant_param.key IS '参数的英文标识 (如: wake_word)';
COMMENT ON COLUMN a_assistant_param.label IS '参数在界面上显示的名称 (如: “唤醒词”)';
COMMENT ON COLUMN a_assistant_param.param_type IS '参数控件类型 (字典: param_type), 1-文本, 2-数字, 3-开关, 4-下拉选择, 5-多行文本';
COMMENT ON COLUMN a_assistant_param.default_value IS '参数的默认值';
COMMENT ON COLUMN a_assistant_param.options IS '若为 select 类型，存储选项 (JSONB)，如 [{"value": "v1", "label": "选项1"}]';
COMMENT ON COLUMN a_assistant_param.description IS '参数的提示或说明文字';
COMMENT ON COLUMN a_assistant_param.sort_order IS '参数在界面上的排序';
COMMENT ON COLUMN a_assistant_param.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_assistant_param.create_by IS '创建人';
COMMENT ON COLUMN a_assistant_param.update_by IS '修改人';
COMMENT ON COLUMN a_assistant_param.create_time IS '创建时间';
COMMENT ON COLUMN a_assistant_param.update_time IS '更新时间';


-- ==================================================
-- 4. 会员助手实例表 (a_member_assistant)
-- ==================================================
CREATE TABLE a_member_assistant (
    id                  BIGSERIAL PRIMARY KEY,
    member_id             BIGINT NOT NULL,
    assistant_id        BIGINT NOT NULL,
    template_version    VARCHAR(50) NOT NULL,
    model_id            BIGINT NOT NULL,
    custom_name         VARCHAR(100) NOT NULL,
    settings_override   JSONB,
    is_favorite         BOOLEAN DEFAULT FALSE,
    is_active           BOOLEAN DEFAULT TRUE,
    delete_time         TIMESTAMPTZ,
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_member_assistant_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE,
    CONSTRAINT fk_member_assistant_assistant FOREIGN KEY (assistant_id) REFERENCES a_assistant(id) ON DELETE CASCADE,
    CONSTRAINT fk_member_assistant_model FOREIGN KEY (model_id) REFERENCES a_model(id) ON DELETE SET NULL
);

-- 为软删除优化的唯一索引
CREATE UNIQUE INDEX idx_member_assistant_member_assistant_unique ON a_member_assistant(member_id, assistant_id) WHERE delete_time IS NULL;
-- 常用查询索引
CREATE INDEX idx_member_assistant_member_id ON a_member_assistant(member_id);

COMMENT ON TABLE a_member_assistant IS '会员助手实例表，存储每个会员对自己助手的个性化配置，是助手“模板”的会员级“实例”。';
COMMENT ON COLUMN a_member_assistant.id IS '主键ID';
COMMENT ON COLUMN a_member_assistant.member_id IS '外键，关联 a_member.id';
COMMENT ON COLUMN a_member_assistant.assistant_id IS '外键，关联 a_assistant.id，表示实例化的哪个助手模板';
COMMENT ON COLUMN a_member_assistant.template_version IS '会员添加此助手时，其模板的版本号';
COMMENT ON COLUMN a_member_assistant.model_id IS '会员为此助手实例选择的具体模型ID，关联 a_model.id';
COMMENT ON COLUMN a_member_assistant.custom_name IS '助手实例呢称，默认由系统自动生成';
COMMENT ON COLUMN a_member_assistant.settings_override IS '会员覆盖的参数值 (JSONB)，以 key-value 形式存储，如 {"wake_word": "你好AI"}';
COMMENT ON COLUMN a_member_assistant.is_favorite IS '是否收藏';
COMMENT ON COLUMN a_member_assistant.is_active IS '是否在会员的助手列表中启用';
COMMENT ON COLUMN a_member_assistant.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_member_assistant.create_time IS '创建时间';
COMMENT ON COLUMN a_member_assistant.update_time IS '更新时间';


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
-- 确保函数存在。如果其他脚本已创建，则此命令无操作。
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为新表绑定触发器
CREATE TRIGGER set_assistant_category_update_time
BEFORE UPDATE ON a_assistant_category
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_assistant_update_time
BEFORE UPDATE ON a_assistant
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_assistant_param_update_time
BEFORE UPDATE ON a_assistant_param
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_member_assistant_update_time
BEFORE UPDATE ON a_member_assistant
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- ==================================================
-- 脚本结束
-- ================================================== 