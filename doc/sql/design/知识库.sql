-- ====================================================================================
-- AIShowLab - 知识库 (RAG) 模块数据库架构脚本 (PostgreSQL) v1.0
-- 作者: Gemini
-- 描述: 本脚本定义了知识库功能的核心元数据表。
--      该设计适配将向量存储在外部向量数据库（如 ChromaDB）的架构。
--      v1.1 更新: 为了性能优化，将所有基于字典的 VARCHAR 字段改为 SMALLINT。
-- ====================================================================================


-- ==================================================
-- 1. 知识库主表 (a_knowledge)
-- ==================================================
CREATE TABLE a_knowledge (
    id                  BIGSERIAL PRIMARY KEY,
    member_id             BIGINT NOT NULL,
    name                VARCHAR(100) NOT NULL,
    sort_order      INTEGER DEFAULT 0,
    description         TEXT,
    icon_url            VARCHAR(255),
    embedding_model_id  BIGINT NOT NULL,
    permission_type     SMALLINT DEFAULT 1,
    delete_time         TIMESTAMPTZ,
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_knowledge_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE SET NULL,
    CONSTRAINT fk_knowledge_embedding_model FOREIGN KEY (embedding_model_id) REFERENCES a_model(id) ON DELETE SET NULL,
    CONSTRAINT knowledge_name_not_empty CHECK (trim(name) <> '')
);

CREATE INDEX idx_knowledge_member_id ON a_knowledge(member_id);

COMMENT ON TABLE a_knowledge IS '知识库主表，作为知识的“容器”，定义其所有者、使用的嵌入模型等元信息。';
COMMENT ON COLUMN a_knowledge.id IS '主键ID，可用于对应ChromaDB中的集合名称';
COMMENT ON COLUMN a_knowledge.member_id IS '知识库创建者/所有者的会员ID';
COMMENT ON COLUMN a_knowledge.name IS '知识库名称';
COMMENT ON COLUMN a_knowledge.sort_order IS '排序，desc';
COMMENT ON COLUMN a_knowledge.description IS '知识库详细描述';
COMMENT ON COLUMN a_knowledge.icon_url IS '知识库图标URL';
COMMENT ON COLUMN a_knowledge.embedding_model_id IS '外键，关联a_model.id，指定处理该知识库时使用的嵌入模型';
COMMENT ON COLUMN a_knowledge.permission_type IS '权限类型 (字典: knowledge_permission_type), 1-私有, 2-租户共享';
COMMENT ON COLUMN a_knowledge.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_knowledge.create_time IS '创建时间';
COMMENT ON COLUMN a_knowledge.update_time IS '更新时间';


-- ==================================================
-- 2. 知识库文档表 (a_knowledge_doc)
-- ==================================================
CREATE TABLE a_knowledge_doc (
    id                  BIGSERIAL PRIMARY KEY,
    knowledge_id        BIGINT NOT NULL,
    file_name           VARCHAR(255) NOT NULL,
    file_type           SMALLINT,
    file_size           BIGINT NOT NULL,
    storage_path        TEXT NOT NULL,
    processing_status   SMALLINT NOT NULL DEFAULT 1,
    error_message       TEXT,
    chunk_count         INTEGER NOT NULL,
    char_count          BIGINT NOT NULL,
    delete_time         TIMESTAMPTZ,
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_doc_knowledge FOREIGN KEY (knowledge_id) REFERENCES a_knowledge(id) ON DELETE CASCADE
);

CREATE INDEX idx_doc_knowledge_id ON a_knowledge_doc(knowledge_id);
CREATE INDEX idx_doc_processing_status ON a_knowledge_doc(processing_status);

COMMENT ON TABLE a_knowledge_doc IS '知识库文档表，记录会员上传到某个知识库的原始文件信息及其处理状态。';
COMMENT ON COLUMN a_knowledge_doc.id IS '主键ID';
COMMENT ON COLUMN a_knowledge_doc.knowledge_id IS '外键，关联 a_knowledge.id';
COMMENT ON COLUMN a_knowledge_doc.file_name IS '原始文件名';
COMMENT ON COLUMN a_knowledge_doc.file_type IS '文件MIME类型 (字典: file_mime_type)';
COMMENT ON COLUMN a_knowledge_doc.file_size IS '文件大小（字节）';
COMMENT ON COLUMN a_knowledge_doc.storage_path IS '文件在对象存储（如S3）中的路径或标识';
COMMENT ON COLUMN a_knowledge_doc.processing_status IS '文档处理状态 (字典: doc_processing_status), 1-上传中, 2-待处理, 3-切分中, 4-向量化, 5-就绪, 6-错误';
COMMENT ON COLUMN a_knowledge_doc.error_message IS '若处理失败，记录错误信息';
COMMENT ON COLUMN a_knowledge_doc.chunk_count IS '成功切分的文本块数量';
COMMENT ON COLUMN a_knowledge_doc.char_count IS '文档的总字符数';
COMMENT ON COLUMN a_knowledge_doc.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_knowledge_doc.create_time IS '创建时间';
COMMENT ON COLUMN a_knowledge_doc.update_time IS '更新时间';


-- ==================================================
-- 3. 文档分块表 (a_knowledge_doc_chunk)
-- ==================================================
CREATE TABLE a_knowledge_doc_chunk (
    id                  BIGSERIAL PRIMARY KEY,
    doc_id              BIGINT NOT NULL,
    content             TEXT NOT NULL,
    char_count          INTEGER NOT NULL,
    metadata            JSONB,
    delete_time         TIMESTAMPTZ,
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_chunk_doc FOREIGN KEY (doc_id) REFERENCES a_knowledge_doc(id) ON DELETE CASCADE
);

CREATE INDEX idx_chunk_doc_id ON a_knowledge_doc_chunk(doc_id);

COMMENT ON TABLE a_knowledge_doc_chunk IS '文档分块表，存储文档被切分后的文本块原文和元数据。这是RAG检索内容的“事实源”。';
COMMENT ON COLUMN a_knowledge_doc_chunk.id IS '主键ID，用于在外部向量数据库(如ChromaDB)中作为向量的唯一标识符';
COMMENT ON COLUMN a_knowledge_doc_chunk.doc_id IS '外键，关联 a_knowledge_doc.id';
COMMENT ON COLUMN a_knowledge_doc_chunk.content IS '文档块的原始文本内容';
COMMENT ON COLUMN a_knowledge_doc_chunk.char_count IS '当前文本块的字符数';
COMMENT ON COLUMN a_knowledge_doc_chunk.metadata IS '与该文本块相关的元数据 (JSONB)，如来源页码、标题等';
COMMENT ON COLUMN a_knowledge_doc_chunk.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_knowledge_doc_chunk.create_time IS '创建时间';
COMMENT ON COLUMN a_knowledge_doc_chunk.update_time IS '更新时间';


-- ==================================================
-- 自动更新 update_time 时间戳的函数和触发器
-- ==================================================
-- 确保函数存在。如果其他脚本已创建，则此命令无操作。
CREATE OR REPLACE FUNCTION trigger_set_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为新表绑定触发器
CREATE TRIGGER set_knowledge_update_time
BEFORE UPDATE ON a_knowledge
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_knowledge_doc_update_time
BEFORE UPDATE ON a_knowledge_doc
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_knowledge_doc_chunk_update_time
BEFORE UPDATE ON a_knowledge_doc_chunk
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

-- ==================================================
-- 脚本结束
-- ================================================== 