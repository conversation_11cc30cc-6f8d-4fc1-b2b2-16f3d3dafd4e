spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  main:
    allow-bean-definition-overriding: true
  data:
    redis:
      host: centos8.vm
      port: 26007
      password: eG9H#2V6$zPv7V
      database: 1
      timeout: 10s
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: org.postgresql.Driver
          url: ***********************************************************************************
          username: vs_dev
          password: e3Hc6Le9Qz7yU2v

# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
      min-request-size: 8192
    response:
      enabled: true

# 暴露监控端点
management:
  endpoints:
    web:
      exposure:
        include: '*'

logging:
  config: classpath:logback.xml