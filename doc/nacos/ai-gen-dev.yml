server:
  port: 9907

spring:
  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ***********************************************************************************
    username: vs_dev
    password: e3Hc6Le9Qz7yU2v

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.gen, ai.showlab.gen
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml
  #database-id-provider:
  #  properties:
  #    MySQL: mysql
  #    PostgreSQL: postgresql

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:9901/${spring.application.name}
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '代码生成接口文档'
    # 描述
    description: '代码生成接口描述'
    # 作者信息
    contact:
      name: RuoYi
      url: https://ruoyi.vip

# 代码生成
gen:
  # 作者
  author: AutoCreate
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: ai.showlab.system
  # 自动去除表前缀，默认是false
  autoRemovePre: true
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: a_
  # 是否允许生成文件覆盖到本地（自定义路径），默认不允许
  allowOverwrite: false