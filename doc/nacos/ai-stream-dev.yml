server:
  port: 9909

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:9901/${spring.application.name}
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '接口模块接口文档'
    # 描述
    description: '接口模块接口描述'
    # 作者信息
    contact:
      name: RuoYi
      url: https://ruoyi.vip

# R2DBC 响应式数据库配置
spring:
  r2dbc:
    url: r2dbc:postgresql://centos8.vm:26028/aishowlab
    username: vs_dev
    password: e3Hc6Le9Qz7yU2v
    pool:
      initial-size: 3
      max-size: 30
      max-idle-time: 30m
      validation-query: SELECT 1

# logging配置
logging:
  level:
    org.springframework.r2dbc: INFO
    #ai.showlab.stream: DEBUG
