server:
  port: 9901

spring:
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ai-auth
          uri: lb://ai-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - CacheRequestBody
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: ai-gen
          uri: lb://ai-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: ai-job
          uri: lb://ai-job
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ai-system
          uri: lb://ai-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: ai-file
          uri: lb://ai-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
        # app接口
        - id: ai-bff
          uri: lb://ai-bff
          predicates:
            - Path=/bff/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice

  # 不校验白名单
  ignore:
    whites:
      - /bff/**
      - /auth/logout
      - /auth/login
      - /auth/register
      - /*/v2/api-docs
      - /*/v3/api-docs
      - /csrf

# springdoc配置
springdoc:
  webjars:
    # 访问前缀
    prefix:
