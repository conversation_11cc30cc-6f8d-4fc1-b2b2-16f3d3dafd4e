server:
  port: 9906

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.system, ai.showlab.system
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  database-id-provider:
    properties:
      MySQL: mysql
      PostgreSQL: postgresql

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:9901/${spring.application.name}
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '系统模块接口文档'
    # 描述
    description: '系统模块接口描述'
    # 作者信息
    contact:
      name: RuoYi
      url: https://ruoyi.vip
