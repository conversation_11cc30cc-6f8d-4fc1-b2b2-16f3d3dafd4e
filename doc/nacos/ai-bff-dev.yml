spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

server:
  port: 9908

mybatis:
  # 搜索指定包别名
  type-aliases-package: ai.showlab.bff.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*.xml
  # TypeHandler 扫描包
  type-handlers-package: ai.showlab.bff.common.typehandler
  #configuration:
  #  map-underscore-to-camel-case: true
  database-id-provider:
    properties:
      PostgreSQL: postgresql

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:9901/${spring.application.name}
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '接口模块接口文档'
    # 描述
    description: '接口模块接口描述'
    # 作者信息
    contact:
      name: RuoYi
      url: https://ruoyi.vip

# 应用安全配置
app:
  security:
    duplicate-login:
      # 重复登录处理策略: ALLOW_MULTIPLE(允许多设备), KICK_OLD(踢掉旧登录), REJECT_NEW(拒绝新登录)
      strategy: KICK_OLD
      # 最大同时登录设备数（设置为1来测试重复登录功能）
      max-concurrent-sessions: 1
      # 是否检查同设备重复登录（true=同设备也受限制，false=同设备不受限制）
      check-same-device: true

